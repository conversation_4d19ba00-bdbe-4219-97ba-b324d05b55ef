import {
  DarkTheme,
  DefaultTheme,
  ThemeProvider,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import "react-native-reanimated";

import { useColorScheme } from "@/hooks/useColorScheme";
import { useSplashScreen } from "@/hooks/useSplashScreen";
import SplashScreen from "@/components/SplashScreen";

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const { isReady, showCustomSplash, onCustomSplashFinish } = useSplashScreen();
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  if (!loaded || !isReady) {
    // Async font loading only occurs in development.
    return null;
  }

  if (showCustomSplash) {
    return <SplashScreen onFinish={onCustomSplashFinish} />;
  }

  return (
    <ThemeProvider value={colorScheme === "dark" ? DarkTheme : DefaultTheme}>
      <Stack>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </ThemeProvider>
  );
}
