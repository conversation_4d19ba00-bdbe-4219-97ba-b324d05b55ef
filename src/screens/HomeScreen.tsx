import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/Ionicons';

export default function HomeScreen() {
  return (
    <ScrollView style={styles.container}>
      {/* Header Section */}
      <LinearGradient
        colors={['#1BA3C7', '#0891B2']}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <View style={styles.welcomeSection}>
            <Text style={styles.welcomeText}>Welcome to</Text>
            <Text style={styles.appName}>NurServ</Text>
            <Text style={styles.tagline}>Expert Nursing Care at Your Doorstep</Text>
          </View>
          
          {/* Logo */}
          <View style={styles.logoContainer}>
            <View style={styles.nurseCap}>
              <View style={styles.capShape} />
              <View style={styles.crossVertical} />
              <View style={styles.crossHorizontal} />
            </View>
            <View style={styles.lettersContainer}>
              <Text style={styles.letterN}>N</Text>
              <Text style={styles.letterV}>V</Text>
            </View>
          </View>
        </View>
      </LinearGradient>

      {/* Services Section */}
      <View style={styles.servicesSection}>
        <Text style={styles.sectionTitle}>Our Services</Text>
        
        <View style={styles.servicesGrid}>
          <TouchableOpacity style={styles.serviceCard}>
            <Icon name="medical" size={40} color="#1BA3C7" />
            <Text style={styles.serviceTitle}>Home Nursing</Text>
            <Text style={styles.serviceDescription}>Professional nursing care at home</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.serviceCard}>
            <Icon name="heart" size={40} color="#1BA3C7" />
            <Text style={styles.serviceTitle}>Health Monitoring</Text>
            <Text style={styles.serviceDescription}>Regular health check-ups</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.serviceCard}>
            <Icon name="fitness" size={40} color="#1BA3C7" />
            <Text style={styles.serviceTitle}>Physiotherapy</Text>
            <Text style={styles.serviceDescription}>Recovery and rehabilitation</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.serviceCard}>
            <Icon name="medical-outline" size={40} color="#1BA3C7" />
            <Text style={styles.serviceTitle}>Medication</Text>
            <Text style={styles.serviceDescription}>Medicine delivery & management</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        
        <TouchableOpacity style={styles.actionButton}>
          <LinearGradient
            colors={['#1BA3C7', '#0891B2']}
            style={styles.actionGradient}
          >
            <Icon name="add-circle" size={24} color="white" />
            <Text style={styles.actionText}>Book a Nurse</Text>
          </LinearGradient>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton}>
          <LinearGradient
            colors={['#1BA3C7', '#0891B2']}
            style={styles.actionGradient}
          >
            <Icon name="call" size={24} color="white" />
            <Text style={styles.actionText}>Emergency Call</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  headerGradient: {
    paddingBottom: 30,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  welcomeSection: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 16,
    color: 'white',
    opacity: 0.9,
  },
  appName: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    marginVertical: 5,
  },
  tagline: {
    fontSize: 14,
    color: 'white',
    opacity: 0.8,
    lineHeight: 18,
  },
  logoContainer: {
    alignItems: 'center',
  },
  nurseCap: {
    width: 40,
    height: 20,
    marginBottom: 10,
    position: 'relative',
  },
  capShape: {
    width: 40,
    height: 15,
    backgroundColor: 'white',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 5,
    borderBottomRightRadius: 5,
  },
  crossVertical: {
    position: 'absolute',
    top: 4,
    left: 17.5,
    width: 5,
    height: 10,
    backgroundColor: '#1BA3C7',
  },
  crossHorizontal: {
    position: 'absolute',
    top: 6.5,
    left: 12.5,
    width: 15,
    height: 5,
    backgroundColor: '#1BA3C7',
  },
  lettersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  letterN: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginRight: 2,
  },
  letterV: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
  },
  servicesSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  servicesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  serviceCard: {
    width: '48%',
    backgroundColor: 'white',
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  serviceTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 10,
    marginBottom: 5,
  },
  serviceDescription: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    lineHeight: 16,
  },
  quickActions: {
    padding: 20,
  },
  actionButton: {
    marginBottom: 15,
    borderRadius: 12,
    overflow: 'hidden',
  },
  actionGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    paddingHorizontal: 20,
  },
  actionText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white',
    marginLeft: 10,
  },
});
