import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

export default function ExploreScreen() {
  return (
    <ScrollView style={styles.container}>
      {/* Search Section */}
      <View style={styles.searchSection}>
        <View style={styles.searchContainer}>
          <Icon name="search" size={20} color="#666" style={styles.searchIcon} />
          <TextInput
            style={styles.searchInput}
            placeholder="Search for services..."
            placeholderTextColor="#999"
          />
        </View>
      </View>

      {/* Categories */}
      <View style={styles.categoriesSection}>
        <Text style={styles.sectionTitle}>Service Categories</Text>
        
        <View style={styles.categoriesGrid}>
          <TouchableOpacity style={styles.categoryCard}>
            <Icon name="medical" size={30} color="#1BA3C7" />
            <Text style={styles.categoryTitle}>General Nursing</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.categoryCard}>
            <Icon name="heart" size={30} color="#1BA3C7" />
            <Text style={styles.categoryTitle}>Cardiac Care</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.categoryCard}>
            <Icon name="fitness" size={30} color="#1BA3C7" />
            <Text style={styles.categoryTitle}>Physiotherapy</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.categoryCard}>
            <Icon name="baby" size={30} color="#1BA3C7" />
            <Text style={styles.categoryTitle}>Pediatric Care</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.categoryCard}>
            <Icon name="accessibility" size={30} color="#1BA3C7" />
            <Text style={styles.categoryTitle}>Elder Care</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.categoryCard}>
            <Icon name="medical-outline" size={30} color="#1BA3C7" />
            <Text style={styles.categoryTitle}>Wound Care</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Featured Services */}
      <View style={styles.featuredSection}>
        <Text style={styles.sectionTitle}>Featured Services</Text>
        
        <TouchableOpacity style={styles.featuredCard}>
          <View style={styles.featuredContent}>
            <Icon name="home" size={40} color="#1BA3C7" />
            <View style={styles.featuredText}>
              <Text style={styles.featuredTitle}>24/7 Home Nursing</Text>
              <Text style={styles.featuredDescription}>
                Round-the-clock professional nursing care in the comfort of your home
              </Text>
              <Text style={styles.featuredPrice}>Starting from ₹500/hour</Text>
            </View>
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.featuredCard}>
          <View style={styles.featuredContent}>
            <Icon name="pulse" size={40} color="#1BA3C7" />
            <View style={styles.featuredText}>
              <Text style={styles.featuredTitle}>Health Monitoring</Text>
              <Text style={styles.featuredDescription}>
                Regular vital signs monitoring and health assessments
              </Text>
              <Text style={styles.featuredPrice}>Starting from ₹300/visit</Text>
            </View>
          </View>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.featuredCard}>
          <View style={styles.featuredContent}>
            <Icon name="bandage" size={40} color="#1BA3C7" />
            <View style={styles.featuredText}>
              <Text style={styles.featuredTitle}>Wound Care</Text>
              <Text style={styles.featuredDescription}>
                Professional wound cleaning, dressing, and healing support
              </Text>
              <Text style={styles.featuredPrice}>Starting from ₹400/session</Text>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  searchSection: {
    padding: 20,
    backgroundColor: 'white',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 25,
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  categoriesSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryCard: {
    width: '30%',
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  categoryTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 16,
  },
  featuredSection: {
    padding: 20,
  },
  featuredCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  featuredContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  featuredText: {
    flex: 1,
    marginLeft: 15,
  },
  featuredTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  featuredDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
    marginBottom: 8,
  },
  featuredPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1BA3C7',
  },
});
