{"version": 3, "file": "withLayoutContext.js", "sourceRoot": "", "sources": ["../../src/layouts/withLayoutContext.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,0DAqEC;AAgCD,8CAmCC;AA1JD,+CAUe;AAEf,oCAAyC;AAEzC,8CAA8D;AAC9D,kDAAwE;AACxE,4CAAmD;AAEnD,SAAgB,uBAAuB,CACrC,QAAmB,EACnB,EACE,iBAAiB,EACjB,UAAU,MAKR,EAAE;IAEN,OAAO,IAAA,eAAO,EAAC,GAAG,EAAE;QAClB,MAAM,cAAc,GAAU,EAAE,CAAC;QAEjC,MAAM,OAAO,GAAuC,EAAE,CAAC;QACvD,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAU,CAAC;QAE3C,SAAS,YAAY,CAAC,KAAgB,EAAE,OAAO,GAAG,KAAK;YACrD,IAAI,IAAA,iBAAQ,EAAC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC;gBAChC,IAAI,OAAO,EAAE,CAAC;oBACZ,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACzC,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBAC5B,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,IAAA,mCAAuB,EAAC,KAAK,CAAC,EAAE,CAAC;gBACnC,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;oBACtB,gBAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,cAAc,EAAE,EAAE,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC;gBAC3F,CAAC;qBAAM,CAAC;oBACN,gBAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,cAAc,EAAE,EAAE;wBACxD,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;oBACrC,CAAC,CAAC,CAAC;gBACL,CAAC;gBACD,OAAO;YACT,CAAC;YAED,IAAI,iBAAiB,EAAE,CAAC;gBACtB,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC3B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,CAAC,IAAI,CACV,2JAA2J,UAAU,WAAW,CACjL,CAAC;YAEF,OAAO,IAAI,CAAC;QACd,CAAC;QAED,gBAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC;QAE3D,mCAAmC;QACnC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAC1C,iCAAiC;YACjC,MAAM,KAAK,GAAG,OAAO,EAAE,GAAG,CACxB,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,CACpF,CAAC;YACF,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE,CAAC;gBAClD,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,KAAK,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO;YACP,QAAQ,EAAE,cAAc;YACxB,gBAAgB;SACjB,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;AACjB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,SAAgB,iBAAiB,CAK/B,GAAM,EAAE,SAAqD;IAC7D,OAAO,MAAM,CAAC,MAAM,CAClB,IAAA,kBAAU,EAAC,CAAC,EAAE,QAAQ,EAAE,mBAAmB,EAAE,GAAG,KAAK,EAAO,EAAE,GAAG,EAAE,EAAE;QACnE,MAAM,UAAU,GAAG,IAAA,qBAAa,GAAE,CAAC;QAEnC,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,uBAAuB,CAAC,mBAAmB,EAAE;YACjF,UAAU;SACX,CAAC,CAAC;QAEH,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAEjE,MAAM,MAAM,GAAG,IAAA,6BAAgB,EAAC,SAAS,IAAI,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAEnE,uDAAuD;QACvD,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAG,CAAC;IACxE,CAAC,CAAC,EACF;QACE,MAAM,EAAN,eAAM;QACN,SAAS,EAAT,qBAAS;KACV,CAMF,CAAC;AACJ,CAAC", "sourcesContent": ["import { EventMapBase, NavigationState } from '@react-navigation/native';\nimport React, {\n  Children,\n  forwardRef,\n  ComponentProps,\n  ComponentType,\n  ForwardRefExoticComponent,\n  PropsWithoutRef,\n  ReactNode,\n  RefAttributes,\n  useMemo,\n} from 'react';\n\nimport { useContextKey } from '../Route';\nimport { PickPartial } from '../types';\nimport { useSortedScreens, ScreenProps } from '../useScreens';\nimport { isProtectedReactElement, Protected } from '../views/Protected';\nimport { isScreen, Screen } from '../views/Screen';\n\nexport function useFilterScreenChildren(\n  children: ReactNode,\n  {\n    isCustomNavigator,\n    contextKey,\n  }: {\n    isCustomNavigator?: boolean;\n    /** Used for sending developer hints */\n    contextKey?: string;\n  } = {}\n) {\n  return useMemo(() => {\n    const customChildren: any[] = [];\n\n    const screens: (ScreenProps & { name: string })[] = [];\n    const protectedScreens = new Set<string>();\n\n    function flattenChild(child: ReactNode, exclude = false) {\n      if (isScreen(child, contextKey)) {\n        if (exclude) {\n          protectedScreens.add(child.props.name);\n        } else {\n          screens.push(child.props);\n        }\n        return;\n      }\n\n      if (isProtectedReactElement(child)) {\n        if (child.props.guard) {\n          Children.forEach(child.props.children, (protectedChild) => flattenChild(protectedChild));\n        } else {\n          Children.forEach(child.props.children, (protectedChild) => {\n            flattenChild(protectedChild, true);\n          });\n        }\n        return;\n      }\n\n      if (isCustomNavigator) {\n        customChildren.push(child);\n        return null;\n      }\n\n      console.warn(\n        `Layout children must be of type Screen, all other children are ignored. To use custom children, create a custom <Layout />. Update Layout Route at: \"app${contextKey}/_layout\"`\n      );\n\n      return null;\n    }\n\n    Children.forEach(children, (child) => flattenChild(child));\n\n    // Add an assertion for development\n    if (process.env.NODE_ENV !== 'production') {\n      // Assert if names are not unique\n      const names = screens?.map(\n        (screen) => screen && typeof screen === 'object' && 'name' in screen && screen.name\n      );\n      if (names && new Set(names).size !== names.length) {\n        throw new Error('Screen names must be unique: ' + names);\n      }\n    }\n\n    return {\n      screens,\n      children: customChildren,\n      protectedScreens,\n    };\n  }, [children]);\n}\n\n/**\n * Returns a navigator that automatically injects matched routes and renders nothing when there are no children.\n * Return type with `children` prop optional.\n * \n * Enables use of other built-in React Navigation navigators and other navigators built with the React Navigation custom navigator API.\n *\n *  @example\n * ```tsx app/_layout.tsx\n * import { ParamListBase, TabNavigationState } from \"@react-navigation/native\";\n * import {\n *   createMaterialTopTabNavigator,\n *   MaterialTopTabNavigationOptions,\n *   MaterialTopTabNavigationEventMap,\n * } from \"@react-navigation/material-top-tabs\";\n * import { withLayoutContext } from \"expo-router\";\n * \n * const MaterialTopTabs = createMaterialTopTabNavigator();\n * \n * const ExpoRouterMaterialTopTabs = withLayoutContext<\n *   MaterialTopTabNavigationOptions,\n *   typeof MaterialTopTabs.Navigator,\n *   TabNavigationState<ParamListBase>,\n *   MaterialTopTabNavigationEventMap\n * >(MaterialTopTabs.Navigator);\n\n * export default function TabLayout() {\n *   return <ExpoRouterMaterialTopTabs />;\n * }\n * ```\n */\nexport function withLayoutContext<\n  TOptions extends object,\n  T extends ComponentType<any>,\n  TState extends NavigationState,\n  TEventMap extends EventMapBase,\n>(Nav: T, processor?: (options: ScreenProps[]) => ScreenProps[]) {\n  return Object.assign(\n    forwardRef(({ children: userDefinedChildren, ...props }: any, ref) => {\n      const contextKey = useContextKey();\n\n      const { screens, protectedScreens } = useFilterScreenChildren(userDefinedChildren, {\n        contextKey,\n      });\n\n      const processed = processor ? processor(screens ?? []) : screens;\n\n      const sorted = useSortedScreens(processed ?? [], protectedScreens);\n\n      // Prevent throwing an error when there are no screens.\n      if (!sorted.length) {\n        return null;\n      }\n\n      return <Nav {...props} id={contextKey} ref={ref} children={sorted} />;\n    }),\n    {\n      Screen,\n      Protected,\n    }\n  ) as ForwardRefExoticComponent<\n    PropsWithoutRef<PickPartial<ComponentProps<T>, 'children'>> & RefAttributes<unknown>\n  > & {\n    Screen: (props: ScreenProps<TOptions, TState, TEventMap>) => null;\n    Protected: typeof Protected;\n  };\n}\n"]}