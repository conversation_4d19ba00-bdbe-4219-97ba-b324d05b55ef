{"version": 3, "file": "TabsClient.js", "sourceRoot": "", "sources": ["../../src/layouts/TabsClient.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;;;AAEb,+DAIuC;AAEvC,kDAA8C;AAC9C,+CAAmD;AAEnD,2DAAwD;AACxD,uCAAoC;AAEpC,2CAAgD;AAChD,kDAA+C;AAE/C,gDAAgD;AAChD,MAAM,kBAAkB,GAAG,IAAA,sCAAwB,GAAE,CAAC,SAAS,CAAC;AAMhE,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAKhC,kBAAkB,EAAE,CAAC,OAAO,EAAE,EAAE;IAChC,oCAAoC;IACpC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;QAC5B,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK,UAAU,IAAI,MAAM,CAAC,OAAO,EAAE,IAAI,KAAK,SAAS,EAAE,CAAC;YAC/E,MAAM,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC;YAC5C,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;YACD,OAAO;gBACL,GAAG,MAAM;gBACT,OAAO,EAAE;oBACP,GAAG,OAAO;oBACV,eAAe,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe;oBAC7E,6DAA6D;oBAC7D,YAAY,EAAE,CAAC,KAAK,EAAE,EAAE;wBACtB,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;4BACjB,OAAO,IAAI,CAAC;wBACd,CAAC;wBACD,MAAM,QAAQ,GACZ,uBAAQ,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,wBAAS,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,wBAAS,CAAC,CAAC;wBACnF,+FAA+F;wBAC/F,0EAA0E;wBAC1E,OAAO,CACL,CAAC,WAAI,CACH,IAAK,KAAa,CAAC,CACnB,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,KAAK,CAAC,KAAY,CAAC,CAAC,CACjD,IAAI,CAAC,CAAC,IAAI,CAAC,CACX,OAAO,CAAC,CAAC,uBAAQ,CAAC,EAAE,KAAK,KAAK,CAAC,CAC/B,QAAQ,CAAC,CAAC,QAAQ,CAAC,EACnB,CACH,CAAC;oBACJ,CAAC;iBACF;aACF,CAAC;QACJ,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CACxB,CAAC,KAAsC,EAAE,EAAE;IACzC,OAAO,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,CAAC,eAAe,CAAC,CAAC,6BAAiB,CAAC,EAAG,CAAC;AACrE,CAAC,EACD;IACE,MAAM,EAAE,QAAQ,CAAC,MAAM;IACvB,SAAS,EAAT,qBAAS;CACV,CACF,CAAC;AAEF,kBAAe,IAAI,CAAC", "sourcesContent": ["'use client';\n\nimport {\n  BottomTabNavigationEventMap,\n  BottomTabNavigationOptions,\n  createBottomTabNavigator,\n} from '@react-navigation/bottom-tabs';\nimport { ParamListBase, TabNavigationState } from '@react-navigation/native';\nimport React, { ComponentProps } from 'react';\nimport { Pressable, Platform } from 'react-native';\n\nimport { withLayoutContext } from './withLayoutContext';\nimport { Link } from '../link/Link';\nimport { Href } from '../types';\nimport { tabRouterOverride } from './TabRouter';\nimport { Protected } from '../views/Protected';\n\n// This is the only way to access the navigator.\nconst BottomTabNavigator = createBottomTabNavigator().Navigator;\n\nexport type BottomTabNavigator = typeof BottomTabNavigator;\n\ntype TabsProps = BottomTabNavigationOptions & { href?: Href | null };\n\nconst ExpoTabs = withLayoutContext<\n  TabsProps,\n  typeof BottomTabNavigator,\n  TabNavigationState<ParamListBase>,\n  BottomTabNavigationEventMap\n>(BottomTabNavigator, (screens) => {\n  // Support the `href` shortcut prop.\n  return screens.map((screen) => {\n    if (typeof screen.options !== 'function' && screen.options?.href !== undefined) {\n      const { href, ...options } = screen.options;\n      if (options.tabBarButton) {\n        throw new Error('Cannot use `href` and `tabBarButton` together.');\n      }\n      return {\n        ...screen,\n        options: {\n          ...options,\n          tabBarItemStyle: href == null ? { display: 'none' } : options.tabBarItemStyle,\n          // @ts-expect-error: TODO(@kitten): This isn't properly typed\n          tabBarButton: (props) => {\n            if (href == null) {\n              return null;\n            }\n            const children =\n              Platform.OS === 'web' ? props.children : <Pressable>{props.children}</Pressable>;\n            // TODO: React Navigation types these props as Animated.WithAnimatedValue<StyleProp<ViewStyle>>\n            //       While Link expects a TextStyle. We need to reconcile these types.\n            return (\n              <Link\n                {...(props as any)}\n                style={[{ display: 'flex' }, props.style as any]}\n                href={href}\n                asChild={Platform.OS !== 'web'}\n                children={children}\n              />\n            );\n          },\n        },\n      };\n    }\n    return screen;\n  });\n});\n\nconst Tabs = Object.assign(\n  (props: ComponentProps<typeof ExpoTabs>) => {\n    return <ExpoTabs {...props} UNSTABLE_router={tabRouterOverride} />;\n  },\n  {\n    Screen: ExpoTabs.Screen,\n    Protected,\n  }\n);\n\nexport default Tabs;\n"]}