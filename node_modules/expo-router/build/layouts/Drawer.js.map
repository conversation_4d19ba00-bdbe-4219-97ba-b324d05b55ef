{"version": 3, "file": "Drawer.js", "sourceRoot": "", "sources": ["../../src/layouts/Drawer.tsx"], "names": [], "mappings": ";;;;;;AAAA,kEAAoC;AAK3B,iBALF,sBAAM,CAKE;AAJf,4CAAyC;AAEzC,sBAAM,CAAC,MAAM,GAAG,eAAM,CAAC;AAIvB,kBAAe,sBAAM,CAAC", "sourcesContent": ["import Drawer from './DrawerClient';\nimport { Screen } from '../views/Screen';\n\nDrawer.Screen = Screen;\n\nexport { Drawer };\n\nexport default Drawer;\n"]}