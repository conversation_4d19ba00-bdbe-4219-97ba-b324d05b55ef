{"version": 3, "file": "Stack.js", "sourceRoot": "", "sources": ["../../src/layouts/Stack.tsx"], "names": [], "mappings": ";;;;;;AAAA,gEAAkC;AAKzB,gBALF,qBAAK,CAKE;AAJd,4CAAyC;AAEzC,qBAAK,CAAC,MAAM,GAAG,eAAM,CAAC;AAItB,kBAAe,qBAAK,CAAC", "sourcesContent": ["import Stack from './StackClient';\nimport { Screen } from '../views/Screen';\n\nStack.Screen = Screen;\n\nexport { Stack };\n\nexport default Stack;\n"]}