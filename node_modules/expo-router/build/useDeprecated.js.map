{"version": 3, "file": "useDeprecated.js", "sourceRoot": "", "sources": ["../src/useDeprecated.ts"], "names": [], "mappings": ";;AAYA,kCASC;AAED,sCAEC;AAzBD,iCAAwC;AACxC,+CAAwC;AAExC,6FAA6F;AAC7F,mDAAmD;AACnD,MAAM,OAAO,GAAG,uBAAQ,CAAC,MAAM,CAAC;IAC9B,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;IAC7C,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,OAAO,MAAM,KAAK,WAAW;CAChF,CAAC,CAAC;AAEH,MAAM,MAAM,GAAG,IAAI,GAAG,EAAU,CAAC;AAEjC,SAAgB,WAAW,CAAC,OAAe,EAAE,QAAiB,IAAI,EAAE,GAAG,GAAG,OAAO;IAC/E,8DAA8D;IAC9D,gEAAgE;IAChE,IAAA,uBAAe,EAAC,GAAG,EAAE;QACnB,IAAI,KAAK,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxB,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;AACd,CAAC;AAED,SAAgB,aAAa,CAAC,OAAe,EAAE,QAAiB,IAAI,EAAE,GAAG,GAAG,OAAO;IACjF,OAAO,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,gBAAgB,OAAO,EAAE,CAAC,CAAC;AAC5D,CAAC", "sourcesContent": ["import { useLayoutEffect } from 'react';\nimport { Platform } from 'react-native';\n\n// Node environment may render in multiple processes causing the warning to log mutiple times\n// Hence we skip the warning in these environments.\nconst canWarn = Platform.select({\n  native: process.env.NODE_ENV !== 'production',\n  default: process.env.NODE_ENV !== 'production' && typeof window !== 'undefined',\n});\n\nconst warned = new Set<string>();\n\nexport function useWarnOnce(message: string, guard: unknown = true, key = message) {\n  // useLayoutEffect typically doesn't run in node environments.\n  // Combined with skipWarn, this should prevent unwanted warnings\n  useLayoutEffect(() => {\n    if (guard && canWarn && !warned.has(key)) {\n      warned.add(key);\n      console.warn(message);\n    }\n  }, [guard]);\n}\n\nexport function useDeprecated(message: string, guard: unknown = true, key = message) {\n  return useWarnOnce(key, guard, `Expo Router: ${message}`);\n}\n"]}