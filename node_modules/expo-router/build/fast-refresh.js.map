{"version": 3, "file": "fast-refresh.js", "sourceRoot": "", "sources": ["../src/fast-refresh.ts"], "names": [], "mappings": ";;AAEA;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;IAC3C;IACE,gCAAgC;IAChC,OAAO,uBAAuB,KAAK,WAAW;QAC9C,4CAA4C;QAC5C,MAAM,CAAC,uBAAuB,GAAG,gBAAgB,CAAC,EAClD,CAAC;QACD,sGAAsG;QACtG,sEAAsE;QACtE,MAAM,OAAO,GAAG,MAAM,CAAC,uBAAuB,GAAG,gBAAgB,CAAC,CAAC;QACnE,mCAAmC;QACnC,MAAM,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,CAAC;QAC5D,0CAA0C;QAC1C,MAAM,iBAAiB,GAAG,IAAI,OAAO,EAAE,CAAC;QAExC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE;YACrB;;;;eAIG;YACH,qBAAqB,CAAC,KAAU;gBAC9B,IAAI,CAAC;oBACH,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;wBAC9B,IAAI,mBAAmB,IAAI,KAAK,EAAE,CAAC;4BACjC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;wBACjD,CAAC;wBACD,IAAI,eAAe,IAAI,KAAK,EAAE,CAAC;4BAC7B,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;wBAC7C,CAAC;wBACD,IAAI,sBAAsB,IAAI,KAAK,EAAE,CAAC;4BACpC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;wBACpD,CAAC;oBACH,CAAC;gBACH,CAAC;gBAAC,MAAM,CAAC;oBACP,6EAA6E;oBAC7E,8EAA8E;oBAC9E,4EAA4E;oBAC5E,4CAA4C;gBAC9C,CAAC;gBACD,OAAO,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,qBAAqB,CAAC,KAAK,CAAC,CAAC;YACtE,CAAC;SACF,CAAC,CAAC;IACL,CAAC;AACH,CAAC", "sourcesContent": ["declare let __METRO_GLOBAL_PREFIX__: string;\n\n/**\n * This is a hack for Expo Router to support Fast Refresh on _layout files\n *\n * Fast Refresh only works when:\n *  - Files only export React Components\n *  - All inverse dependencies only export React Components\n *\n * Expo Router's _layout files support exporting both 'unstable_settings' and 'ErrorBoundary'\n *\n * 'unstable_settings':\n *  - This is a plain object, so it will break Fast Refresh\n *\n * 'ErrorBoundary'\n *  - While this is a React component, it is imported from 'expo-router'\n *  - 'expo-router' has an inverse dependency on _ctx, which is a require.context object\n *\n * 'generateStaticParams'\n *  - This is a function that is not a React Component, so it will break Fast Refresh\n *\n *\n * To resolve this issue, we extend ReactRefresh to flag these exports as React components\n *\n * @see https://reactnative.dev/docs/fast-refresh\n */\nif (process.env.NODE_ENV === 'development') {\n  if (\n    // Should be a string at runtime\n    typeof __METRO_GLOBAL_PREFIX__ !== 'undefined' &&\n    // Should be set by Metro's require polyfill\n    global[__METRO_GLOBAL_PREFIX__ + '__ReactRefresh']\n  ) {\n    // source: https://github.com/facebook/metro/blob/main/packages/metro-runtime/src/polyfills/require.js\n    // TODO(@kitten): Add type for this and use `globalThis` over `global`\n    const Refresh = global[__METRO_GLOBAL_PREFIX__ + '__ReactRefresh'];\n    // Keep a reference to the original\n    const isLikelyComponentType = Refresh.isLikelyComponentType;\n    // Modules can be dereferenced at any time\n    const expoRouterExports = new WeakSet();\n\n    Object.assign(Refresh, {\n      /*\n       * isLikelyComponentType is called twice.\n       *   1. Initially with a modules export object\n       *   2. With each individual export of a module\n       */\n      isLikelyComponentType(value: any) {\n        try {\n          if (typeof value === 'object') {\n            if ('unstable_settings' in value) {\n              expoRouterExports.add(value.unstable_settings);\n            }\n            if ('ErrorBoundary' in value) {\n              expoRouterExports.add(value.ErrorBoundary);\n            }\n            if ('generateStaticParams' in value) {\n              expoRouterExports.add(value.generateStaticParams);\n            }\n          }\n        } catch {\n          // Ignore - we're just trying to avoid breaking Fast Refresh by using exports\n          // that aren't JS objects valid as keys for the WeakSet - like we've seen with\n          // some JSI::HostObject instances that are exported in a module - see #33670\n          // https://github.com/expo/expo/issues/33670\n        }\n        return expoRouterExports.has(value) || isLikelyComponentType(value);\n      },\n    });\n  }\n}\n\n// Export an empty object so TypeScript doesn't consider this an ambient module\nexport {};\n"]}