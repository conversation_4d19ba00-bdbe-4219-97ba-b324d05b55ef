{"version": 3, "file": "LocationProvider.js", "sourceRoot": "", "sources": ["../src/LocationProvider.tsx"], "names": [], "mappings": ";;AAaA,sDAeC;AA0BD,wDAkBC;AAvED,0EAA6D;AAY7D,SAAgB,qBAAqB,CACnC,gBAAkF,EAClF,KAAY,EACZ,OAAgB;IAEhB,MAAM,EAAE,IAAI,EAAE,GAAG,gBAAgB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAChD,MAAM,SAAS,GAAG,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAEhD,OAAO;QACL,kEAAkE;QAClE,mBAAmB,EAAE,IAAI;QACzB,QAAQ,EAAE,IAAA,qCAAY,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QACrD,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC;QAC3B,GAAG,sBAAsB,CAAC,SAAS,EAAE,OAAO,CAAC;KAC9C,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,KAAY;IAC/B,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACnE,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;QAChB,OAAO,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAED,+EAA+E;IAC/E,IAAI,KAAK,CAAC,MAAM,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;QAC7C,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,KAAK,OAAO,CAAC;IACzC,CAAC;IAED,yDAAyD;IACzD,+EAA+E;IAC/E,qEAAqE;IACrE,qCAAqC;IACrC,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC;QAAE,OAAO,IAAI,CAAC;IAEhD,2GAA2G;IAC3G,kEAAkE;IAElE,OAAO,KAAK,CAAC;AACf,CAAC;AAED,mEAAmE;AACnE,SAAgB,sBAAsB,CACpC,EACE,IAAI,EAAE,SAAS,EACf,MAAM,GAIP,EACD,OAAgB;IAEhB,MAAM,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACxC,OAAO;QACL,gCAAgC;QAChC,QAAQ,EAAE,IAAA,qCAAY,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC;QAC5F,6EAA6E;QAC7E,8CAA8C;QAC9C,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC;KAC7B,CAAC;AACJ,CAAC;AAED,SAAS,YAAY,CAAC,MAA8B;IAClD,MAAM,MAAM,GAAwB,EAAE,CAAC;IAEvC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QAClD,IAAI,CAAC;YACH,IAAI,GAAG,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBAClD,MAAM,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;YACpC,CAAC;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBAChC,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;YACxD,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,GAAG,CAAC,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;QAAC,MAAM,CAAC;YACP,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACtB,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import { type State } from './fork/getPathFromState';\nimport { stripBaseUrl } from './fork/getStateFromPath-forks';\n\ntype SearchParams = Record<string, string | string[]>;\n\nexport type UrlObject = {\n  unstable_globalHref: string;\n  pathname: string;\n  readonly params: SearchParams;\n  segments: string[];\n  isIndex: boolean;\n};\n\nexport function getRouteInfoFromState(\n  getPathFromState: (state: State, asPath: boolean) => { path: string; params: any },\n  state: State,\n  baseUrl?: string\n): UrlObject {\n  const { path } = getPathFromState(state, false);\n  const qualified = getPathFromState(state, true);\n\n  return {\n    // TODO: This may have a predefined origin attached in the future.\n    unstable_globalHref: path,\n    pathname: stripBaseUrl(path, baseUrl).split('?')['0'],\n    isIndex: isIndexPath(state),\n    ...getNormalizedStatePath(qualified, baseUrl),\n  };\n}\n\nfunction isIndexPath(state: State) {\n  const route = state.routes[state.index ?? state.routes.length - 1];\n  if (route.state) {\n    return isIndexPath(route.state);\n  }\n\n  // Index routes on the same level as a layout do not have `index` in their name\n  if (route.params && 'screen' in route.params) {\n    return route.params.screen === 'index';\n  }\n\n  // The `params` key will not exist if there are no params\n  // So we need to do a positive lookahead to check if the route ends with /index\n  // Nested routes that are hoisted will have a name ending with /index\n  // e.g name could be /user/[id]/index\n  if (route.name.match(/.+\\/index$/)) return true;\n\n  // The state will either have params (because there are multiple _layout) or it will be hoisted with a name\n  // If we don't match the above cases, then it's not an index route\n\n  return false;\n}\n\n// TODO: Split up getPathFromState to return all this info at once.\nexport function getNormalizedStatePath(\n  {\n    path: statePath,\n    params,\n  }: {\n    path: string;\n    params: any;\n  },\n  baseUrl?: string\n): Pick<UrlObject, 'segments' | 'params'> {\n  const [pathname] = statePath.split('?');\n  return {\n    // Strip empty path at the start\n    segments: stripBaseUrl(pathname, baseUrl).split('/').filter(Boolean).map(decodeURIComponent),\n    // TODO: This is not efficient, we should generate based on the state instead\n    // of converting to string then back to object\n    params: decodeParams(params),\n  };\n}\n\nfunction decodeParams(params: Record<string, string>) {\n  const parsed: Record<string, any> = {};\n\n  for (const [key, value] of Object.entries(params)) {\n    try {\n      if (key === 'params' && typeof value === 'object') {\n        parsed[key] = decodeParams(value);\n      } else if (Array.isArray(value)) {\n        parsed[key] = value.map((v) => decodeURIComponent(v));\n      } else {\n        parsed[key] = decodeURIComponent(value);\n      }\n    } catch {\n      parsed[key] = value;\n    }\n  }\n\n  return parsed;\n}\n"]}