{"version": 3, "file": "getRoutesSSR.js", "sourceRoot": "", "sources": ["../src/getRoutesSSR.ts"], "names": [], "mappings": ";;;AAiBA,8BAuDC;AAED,wCAQC;AAjFD,mDAA0F;AAI1F;;;;;;;;;;;GAWG;AACH,SAAgB,SAAS,CAAC,aAA6B,EAAE,UAAmB,EAAE;IAC5E,OAAO,IAAA,yBAAa,EAAC,aAAa,EAAE;QAClC,cAAc,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE;YACtC,IAAI,KAAK,KAAK,EAAE,IAAI,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACtC,yCAAyC;gBACzC,OAAO;oBACL,IAAI,EAAE,QAAQ;oBACd,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;wBAChB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI;qBACpB,CAAC;oBACF,8CAA8C;oBAC9C,UAAU,EAAE,sCAAsC;oBAClD,KAAK,EAAE,EAAE;oBACT,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,EAAE;iBACb,CAAC;YACJ,CAAC;iBAAM,IAAI,KAAK,KAAK,UAAU,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;gBACpD,OAAO;oBACL,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;wBAChB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI;qBACpB,CAAC;oBACF,KAAK,EAAE,UAAU;oBACjB,IAAI,EAAE,OAAO;oBACb,UAAU,EAAE,oCAAoC;oBAChD,SAAS,EAAE,IAAI;oBACf,QAAQ,EAAE,IAAI;oBACd,OAAO,EAAE,IAAI;oBACb,QAAQ,EAAE,EAAE;iBACb,CAAC;YACJ,CAAC;iBAAM,IAAI,KAAK,KAAK,YAAY,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;gBACtD,OAAO;oBACL,SAAS,EAAE,GAAG,EAAE,CAAC,CAAC;wBAChB,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI;qBACpB,CAAC;oBACF,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE,YAAY;oBACnB,UAAU,EAAE,sCAAsC;oBAClD,SAAS,EAAE,IAAI;oBACf,QAAQ,EAAE,IAAI;oBACd,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;oBAC7D,QAAQ,EAAE,EAAE;iBACb,CAAC;YACJ,CAAC;iBAAM,IAAI,CAAC,IAAI,KAAK,UAAU,IAAI,IAAI,KAAK,SAAS,CAAC,IAAI,QAAQ,EAAE,CAAC;gBACnE,OAAO;oBACL,GAAG,QAAQ;oBACX,SAAS;wBACP,OAAO,OAAO,CAAC,sBAAsB,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;oBAClE,CAAC;iBACF,CAAC;YACJ,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,yBAAyB,KAAK,cAAc,IAAI,EAAE,CAAC,CAAC;QACtE,CAAC;QACD,GAAG,OAAO;KACX,CAAC,CAAC;AACL,CAAC;AAED,SAAgB,cAAc,CAC5B,aAA6B,EAC7B,UAAmB,EAAE;IAErB,OAAO,SAAS,CAAC,aAAa,EAAE;QAC9B,GAAG,OAAO;QACV,aAAa,EAAE,IAAI;KACpB,CAAC,CAAC;AACL,CAAC;AAED,iDAAqE;AAA5D,gHAAA,eAAe,OAAA;AAAE,kHAAA,iBAAiB,OAAA", "sourcesContent": ["import type { RouteNode } from './Route';\nimport { getRoutes as getRoutesCore, type Options as OptionsCore } from './getRoutesCore';\nimport type { RequireContext } from './types';\n\nexport type Options = Omit<OptionsCore, 'getSystemRoute'>;\n/**\n * Given a Metro context module, return an array of nested routes.\n *\n * This is a two step process:\n *  1. Convert the RequireContext keys (file paths) into a directory tree.\n *      - This should extrapolate array syntax into multiple routes\n *      - Routes are given a specificity score\n *  2. Flatten the directory tree into routes\n *      - Routes in directories without _layout files are hoisted to the nearest _layout\n *      - The name of the route is relative to the nearest _layout\n *      - If multiple routes have the same name, the most specific route is used\n */\nexport function getRoutes(contextModule: RequireContext, options: Options = {}): RouteNode | null {\n  return getRoutesCore(contextModule, {\n    getSystemRoute({ route, type, defaults }) {\n      if (route === '' && type === 'layout') {\n        // Root layout when no layout is defined.\n        return {\n          type: 'layout',\n          loadRoute: () => ({\n            default: () => null,\n          }),\n          // Generate a fake file name for the directory\n          contextKey: 'expo-router/build/views/Navigator.js',\n          route: '',\n          generated: true,\n          dynamic: null,\n          children: [],\n        };\n      } else if (route === '_sitemap' && type === 'route') {\n        return {\n          loadRoute: () => ({\n            default: () => null,\n          }),\n          route: '_sitemap',\n          type: 'route',\n          contextKey: 'expo-router/build/views/Sitemap.js',\n          generated: true,\n          internal: true,\n          dynamic: null,\n          children: [],\n        };\n      } else if (route === '+not-found' && type === 'route') {\n        return {\n          loadRoute: () => ({\n            default: () => null,\n          }),\n          type: 'route',\n          route: '+not-found',\n          contextKey: 'expo-router/build/views/Unmatched.js',\n          generated: true,\n          internal: true,\n          dynamic: [{ name: '+not-found', deep: true, notFound: true }],\n          children: [],\n        };\n      } else if ((type === 'redirect' || type === 'rewrite') && defaults) {\n        return {\n          ...defaults,\n          loadRoute() {\n            return require('./getRoutesRedirects').getRedirectModule(route);\n          },\n        };\n      }\n      throw new Error(`Unknown system route: ${route} and type: ${type}`);\n    },\n    ...options,\n  });\n}\n\nexport function getExactRoutes(\n  contextModule: RequireContext,\n  options: Options = {}\n): RouteNode | null {\n  return getRoutes(contextModule, {\n    ...options,\n    skipGenerated: true,\n  });\n}\n\nexport { generateDynamic, extrapolateGroups } from './getRoutesCore';\n"]}