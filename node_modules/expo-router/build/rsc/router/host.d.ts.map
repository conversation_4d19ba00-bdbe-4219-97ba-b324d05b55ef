{"version": 3, "file": "host.d.ts", "sourceRoot": "", "sources": ["../../../src/rsc/router/host.tsx"], "names": [], "mappings": "AAAA;;;;;;;;GAQG;AAgBH,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAiFvC,KAAK,WAAW,GAAG,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC;AAC3C,KAAK,WAAW,GAAG,CAAC,OAAO,EAAE,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,QAAQ,KAAK,QAAQ,CAAC,KAAK,IAAI,CAAC;AAIhF,QAAA,MAAM,KAAK,MAAM,CAAC;AAClB,QAAA,MAAM,YAAY,MAAM,CAAC;AACzB,QAAA,MAAM,aAAa,MAAM,CAAC;AAE1B,KAAK,UAAU,GAAG;IAChB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC/D,CAAC,YAAY,CAAC,CAAC,EAAE,WAAW,CAAC;IAC7B,CAAC,aAAa,CAAC,CAAC,EAAE,WAAW,GAAG,SAAS,CAAC;CAC3C,CAAC;AAqEF,KAAK,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,GAAG;IACnD,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,SAAS,CAAC;CAC9C,CAAC;AAmCF;;;GAGG;AACH,eAAO,MAAM,aAAa,GACxB,UAAU,MAAM,EAChB,OAAO,OAAO,EAAE,EAChB,uBAA8B,yUAkB/B,CAAC;AAyBF,eAAO,MAAM,QAAQ,GACnB,OAAO,MAAM,EACb,SAAS,OAAO,EAChB,uBAA8B,KAC7B,QA4CF,CAAC;AAqBF,eAAO,MAAM,WAAW,GAAI,OAAO,MAAM,EAAE,SAAS,OAAO,KAAG,IAQ7D,CAAC;AASF,eAAO,MAAM,IAAI,GAAI,8EAOlB;IACD,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,UAAU,CAAC,EAAE,UAAU,CAAC;IACxB,oBAAoB,CAAC,EAAE,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC;IAC/C,QAAQ,EAAE,SAAS,CAAC;CACrB,oFAnBS,MAAM,iBAAiB,eAAe,GAAG,MAAM,KAAK,IAAI,EA2CjE,CAAC;AAEF,eAAO,MAAM,UAAU,gBA7Cb,MAAM,iBAAiB,eAAe,GAAG,MAAM,KAAK,IA6CX,CAAC;AAKpD,eAAO,MAAM,IAAI,GAAI,6BAIlB;IACD,EAAE,EAAE,MAAM,CAAC;IACX,QAAQ,CAAC,EAAE,SAAS,CAAC;IACrB,QAAQ,CAAC,EAAE,SAAS,CAAC;CACtB,8bAaA,CAAC;AAEF,eAAO,MAAM,QAAQ,iBAA6B,CAAC;AAEnD;;;GAGG;AACH,eAAO,MAAM,UAAU,GAAI,wBAAwB;IAAE,QAAQ,EAAE,QAAQ,CAAC;IAAC,QAAQ,EAAE,SAAS,CAAA;CAAE,6FACtB,CAAC"}