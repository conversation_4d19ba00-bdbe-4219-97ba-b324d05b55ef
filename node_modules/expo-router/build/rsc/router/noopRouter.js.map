{"version": 3, "file": "noopRouter.js", "sourceRoot": "", "sources": ["../../../src/rsc/router/noopRouter.ts"], "names": [], "mappings": ";;AAAA,2DAAsD;AAEtD,kBAAe,IAAA,mCAAe,EAAC,KAAK,IAAI,EAAE;IACxC,sGAAsG;AACxG,CAAC,CAAC,CAAC", "sourcesContent": ["import { createExpoPages } from './create-expo-pages';\n\nexport default createExpoPages(async () => {\n  // noop the router for client-only mode. This ensures we skip loading the routes in react-server mode.\n});\n"]}