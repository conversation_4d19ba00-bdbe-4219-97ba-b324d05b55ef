{"version": 3, "file": "path.js", "sourceRoot": "", "sources": ["../../src/rsc/path.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;;AAEH,eAAe;AACf,0EAA0E;AAC1E,0BAA0B;AAC1B,4EAA4E;AAC5E,8BAA8B;AAC9B,qEAAqE;AACrE,6BAA6B;AAE7B,MAAM,0BAA0B,GAAG,gBAAgB,CAAC;AAE7C,MAAM,wBAAwB,GAAG,CAAC,QAAgB,EAAE,EAAE;IAC3D,IAAI,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC9C,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACpD,CAAC;IACD,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QAC7B,OAAO,QAAQ,CAAC;IAClB,CAAC;IACD,OAAO,GAAG,GAAG,QAAQ,CAAC;AACxB,CAAC,CAAC;AARW,QAAA,wBAAwB,4BAQnC;AAEK,MAAM,0BAA0B,GAAG,CAAC,QAAgB,EAAE,EAAE;IAC7D,IAAI,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC9C,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AALW,QAAA,0BAA0B,8BAKrC;AAEK,MAAM,iBAAiB,GAAG,CAAC,QAAgB,EAAE,EAAE,CAAC,SAAS,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC;AAA1E,QAAA,iBAAiB,qBAAyD;AAEvF,yDAAyD;AAClD,MAAM,iBAAiB,GAAG,CAAC,OAAe,EAAE,EAAE;IACnD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QACnC,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;IACpC,CAAC;IAED,MAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;IAE5D,yFAAyF;IACzF,oGAAoG;IACpG,qIAAqI;IACrI,OAAO,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC9C,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;QACxC,CAAC,CAAC,QAAQ,CAAC;AACf,CAAC,CAAC;AAbW,QAAA,iBAAiB,qBAa5B;AAEF,eAAe;AACR,MAAM,QAAQ,GAAG,CAAC,GAAG,KAAe,EAAE,EAAE;IAC7C,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;IAC7C,MAAM,KAAK,GAAI,EAAe,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAC/E,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,OAAO,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QACxB,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC;YACxC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACrB,CAAC;aAAM,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;gBACV,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvB,EAAE,CAAC,CAAC;YACN,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YACrB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,EAAE,CAAC,CAAC;QACN,CAAC;IACH,CAAC;IACD,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC;AAC1D,CAAC,CAAC;AAnBW,QAAA,QAAQ,YAmBnB;AAEK,MAAM,OAAO,GAAG,CAAC,QAAgB,EAAE,EAAE;IAC1C,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;IACxC,OAAO,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAChD,CAAC,CAAC;AAHW,QAAA,OAAO,WAGlB;AAQK,MAAM,iBAAiB,GAAG,CAAC,IAAY,EAAY,EAAE,CAC1D,IAAI;KACD,KAAK,CAAC,GAAG,CAAC;KACV,MAAM,CAAC,OAAO,CAAC;KACf,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;IACZ,IAAI,IAAI,GAAqC,SAAS,CAAC;IACvD,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAC1D,IAAI,MAAM,EAAE,CAAC;QACX,IAAI,GAAG,OAAO,CAAC;QACf,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IACD,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAC1C,IAAI,UAAU,EAAE,CAAC;QACf,IAAI,GAAG,UAAU,CAAC;QAClB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IACD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACxB,CAAC,CAAC,CAAC;AAjBM,QAAA,iBAAiB,qBAiBvB;AAEA,MAAM,cAAc,GAAG,CAC5B,QAAkB,EAClB,QAAgB,EAC0B,EAAE;IAC5C,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACnD,IAAI,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;QACpC,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,OAAO,GAAsC,EAAE,CAAC;IACtD,IAAI,kBAAkB,GAAG,CAAC,CAAC,CAAC;IAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACzC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAE,CAAC;QACpC,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACvB,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvB,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YAC/B,kBAAkB,GAAG,CAAC,CAAC;YACvB,MAAM;QACR,CAAC;aAAM,IAAI,IAAI,EAAE,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAE,CAAC;QAC7B,CAAC;IACH,CAAC;IACD,IAAI,kBAAkB,KAAK,CAAC,CAAC,EAAE,CAAC;QAC9B,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;YACtC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IACD,IAAI,gBAAgB,GAAG,CAAC,CAAC,CAAC;IAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACzC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,CAAC;QAC1D,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACvB,IAAI,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC3C,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;YAC/B,gBAAgB,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;YACzC,MAAM;QACR,CAAC;aAAM,IAAI,IAAI,EAAE,CAAC;YAChB,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAE,CAAC;QACjD,CAAC;IACH,CAAC;IACD,IAAI,kBAAkB,KAAK,CAAC,CAAC,IAAI,gBAAgB,KAAK,CAAC,CAAC,EAAE,CAAC;QACzD,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC3C,CAAC;IACD,MAAM,YAAY,GAAG,QAAQ,CAAC,kBAAkB,CAAE,CAAC,IAAI,CAAC;IACxD,IAAI,YAAY,EAAE,CAAC;QACjB,OAAO,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,kBAAkB,EAAE,gBAAgB,GAAG,CAAC,CAAC,CAAC;IACjF,CAAC;IACD,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAnDW,QAAA,cAAc,kBAmDzB;AAEF;;GAEG;AACI,MAAM,WAAW,GAAG,CAAC,IAAc,EAAE,EAAE;IAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE;QACxC,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,IAAI,KAAK,OAAO,EAAE,CAAC;YAC5B,OAAO,SAAS,CAAC;QACnB,CAAC;aAAM,CAAC;YACN,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;AACjC,CAAC,CAAC;AAXW,QAAA,WAAW,eAWtB", "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n * Copyright © 2024 2023 <PERSON><PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * https://github.com/dai-shi/waku/blob/32d52242c1450b5f5965860e671ff73c42da8bd0/packages/waku/src/lib/utils/path.ts#L1\n */\n\n// Terminology:\n// - filePath: posix-like file path, e.g. `/foo/bar.js` or `c:/foo/bar.js`\n//   This is used by Vite.\n// - fileURL: file URL, e.g. `file:///foo/bar.js` or `file:///c:/foo/bar.js`\n//   This is used by import().\n// - osPath: os dependent path, e.g. `/foo/bar.js` or `c:\\foo\\bar.js`\n//   This is used by node:fs.\n\nconst ABSOLUTE_WIN32_PATH_REGEXP = /^\\/[a-zA-Z]:\\//;\n\nexport const encodeFilePathToAbsolute = (filePath: string) => {\n  if (ABSOLUTE_WIN32_PATH_REGEXP.test(filePath)) {\n    throw new Error('Unsupported absolute file path');\n  }\n  if (filePath.startsWith('/')) {\n    return filePath;\n  }\n  return '/' + filePath;\n};\n\nexport const decodeFilePathFromAbsolute = (filePath: string) => {\n  if (ABSOLUTE_WIN32_PATH_REGEXP.test(filePath)) {\n    return filePath.slice(1);\n  }\n  return filePath;\n};\n\nexport const filePathToFileURL = (filePath: string) => 'file://' + encodeURI(filePath);\n\n/** Return the original \"osPath\" based on the file URL */\nexport const fileURLToFilePath = (fileURL: string) => {\n  if (!fileURL.startsWith('file://')) {\n    throw new Error('Not a file URL');\n  }\n\n  const filePath = decodeURI(fileURL.slice('file://'.length));\n\n  // File URLs are always formatted in POSIX, using a leading `/` (URL pathname) separator.\n  // On POSIX systems, this leading `/` is the root directory, which is valid for absolute file paths.\n  // On UNIX systems, this leading `/` needs to be stripped, and the actual UNIX formatted path is returned - to match Metro's behavior\n  return ABSOLUTE_WIN32_PATH_REGEXP.test(filePath)\n    ? filePath.slice(1).replace(/\\//g, '\\\\')\n    : filePath;\n};\n\n// for filePath\nexport const joinPath = (...paths: string[]) => {\n  const isAbsolute = paths[0]?.startsWith('/');\n  const items = ([] as string[]).concat(...paths.map((path) => path.split('/')));\n  let i = 0;\n  while (i < items.length) {\n    if (items[i] === '.' || items[i] === '') {\n      items.splice(i, 1);\n    } else if (items[i] === '..') {\n      if (i > 0) {\n        items.splice(i - 1, 2);\n        --i;\n      } else {\n        items.splice(i, 1);\n      }\n    } else {\n      ++i;\n    }\n  }\n  return (isAbsolute ? '/' : '') + items.join('/') || '.';\n};\n\nexport const extname = (filePath: string) => {\n  const index = filePath.lastIndexOf('.');\n  return index > 0 ? filePath.slice(index) : '';\n};\n\nexport type PathSpecItem =\n  | { type: 'literal'; name: string }\n  | { type: 'group'; name?: string }\n  | { type: 'wildcard'; name?: string };\nexport type PathSpec = readonly PathSpecItem[];\n\nexport const parsePathWithSlug = (path: string): PathSpec =>\n  path\n    .split('/')\n    .filter(Boolean)\n    .map((name) => {\n      let type: 'literal' | 'group' | 'wildcard' = 'literal';\n      const isSlug = name.startsWith('[') && name.endsWith(']');\n      if (isSlug) {\n        type = 'group';\n        name = name.slice(1, -1);\n      }\n      const isWildcard = name.startsWith('...');\n      if (isWildcard) {\n        type = 'wildcard';\n        name = name.slice(3);\n      }\n      return { type, name };\n    });\n\nexport const getPathMapping = (\n  pathSpec: PathSpec,\n  pathname: string\n): Record<string, string | string[]> | null => {\n  const actual = pathname.split('/').filter(Boolean);\n  if (pathSpec.length > actual.length) {\n    return null;\n  }\n  const mapping: Record<string, string | string[]> = {};\n  let wildcardStartIndex = -1;\n  for (let i = 0; i < pathSpec.length; i++) {\n    const { type, name } = pathSpec[i]!;\n    if (type === 'literal') {\n      if (name !== actual[i]) {\n        return null;\n      }\n    } else if (type === 'wildcard') {\n      wildcardStartIndex = i;\n      break;\n    } else if (name) {\n      mapping[name] = actual[i]!;\n    }\n  }\n  if (wildcardStartIndex === -1) {\n    if (pathSpec.length !== actual.length) {\n      return null;\n    }\n    return mapping;\n  }\n  let wildcardEndIndex = -1;\n  for (let i = 0; i < pathSpec.length; i++) {\n    const { type, name } = pathSpec[pathSpec.length - i - 1]!;\n    if (type === 'literal') {\n      if (name !== actual[actual.length - i - 1]) {\n        return null;\n      }\n    } else if (type === 'wildcard') {\n      wildcardEndIndex = actual.length - i - 1;\n      break;\n    } else if (name) {\n      mapping[name] = actual[actual.length - i - 1]!;\n    }\n  }\n  if (wildcardStartIndex === -1 || wildcardEndIndex === -1) {\n    throw new Error('Invalid wildcard path');\n  }\n  const wildcardName = pathSpec[wildcardStartIndex]!.name;\n  if (wildcardName) {\n    mapping[wildcardName] = actual.slice(wildcardStartIndex, wildcardEndIndex + 1);\n  }\n  return mapping;\n};\n\n/**\n * Transform a path spec to a regular expression.\n */\nexport const path2regexp = (path: PathSpec) => {\n  const parts = path.map(({ type, name }) => {\n    if (type === 'literal') {\n      return name;\n    } else if (type === 'group') {\n      return `([^/]+)`;\n    } else {\n      return `(.*)`;\n    }\n  });\n  return `^/${parts.join('/')}$`;\n};\n"]}