{"version": 3, "file": "rsc-renderer.js", "sourceRoot": "", "sources": ["../../src/rsc/rsc-renderer.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;AAsDH,8BAwKC;AA5ND,kEAAkE;AAElE,iGAAiG;AACjG,2CAAyC;AAGzC,4DAAsF;AAEtF,iCAA2C;AAC3C,0CAAgD;AAChD,qCAAgF;AA0CzE,KAAK,UAAU,SAAS,CAAC,IAAmB,EAAE,IAAmB;IACtE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;IAC5D,MAAM,EAAE,kBAAkB,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;IAE7C,MAAM,EACJ,OAAO,EAAE,EAAE,aAAa,EAAE;IAC1B,mBAAmB;IACnB,WAAW,GACZ,GAAG,OAAgF,CAAC;IAErF,SAAS,cAAc,CAAC,QAAiB,EAAE,SAAiB;QAC1D,MAAM;QACJ,+GAA+G;QAC/G,IAAI;QACJ,gDAAgD;QAChD,0EAA0E;QAC1E,IAAI,GAAG,EAAE,EACV,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAqB,CAAC;QAE7C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAA,wBAAiB,EAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAE7E,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,EAAE,EAAE,QAAQ;YACZ,MAAM,EAAE;gBACN,uFAAuF;gBACvF,+HAA+H;gBAC/H,QAAQ,GAAG,QAAQ;aACpB;YACD,IAAI;YACJ,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;QACH,mJAAmJ;QACnJ,kDAAkD;QAClD,MAAM,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACxD,OAAO,EAAE,EAAE,EAAE,QAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACzE,CAAC;IAED,MAAM,aAAa,GAAsD,IAAI,KAAK,CAChF,EAAE,EACF;QACE,GAAG,CAAC,OAAO,EAAE,SAAiB;YAC5B,OAAO,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;QAC1C,CAAC;KACF,CACF,CAAC;IAEF,MAAM,YAAY,GAAsD,IAAI,KAAK,CAC/E,EAAE,EACF;QACE,GAAG,CAAC,OAAO,EAAE,SAAiB;YAC5B,OAAO,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QACzC,CAAC;KACF,CACF,CAAC;IAEF,sGAAsG;IACtG,MAAM,CAAC,GAAG,uBAAuB,mBAAmB,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC;IAEjF,MAAM,iBAAiB,GAAG,KAAK,EAC7B,OAA4C,EAC5C,KAAa,EACb,MAAe,EACf,EAAE;QACF,MAAM,WAAW,GAAG;YAClB,OAAO,EAAE,OAAO,IAAI,EAAE;YACtB,QAAQ,EAAE,GAAG,EAAE;gBACb,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACrC,CAAC;SACF,CAAC;QACF,OAAO,IAAA,2BAAkB,EAAC,WAAW,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,KAAK,EAAE;gBAC1C,MAAM;gBACN,WAAW;aACZ,CAAC,CAAC;YACH,IAAI,QAAQ,KAAK,IAAI,EAAE,CAAC;gBACtB,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,kCAAkC,GAAG,KAAK,CAAC,CAAC;gBACjE,GAAW,CAAC,UAAU,GAAG,GAAG,CAAC;gBAC9B,MAAM,GAAG,CAAC;YACZ,CAAC;YACD,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC7D,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YACD,OAAO,IAAA,+BAAsB,EAAC,QAAQ,EAAE,aAAa,EAAE;gBACrD,OAAO;aACR,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,MAAM,2BAA2B,GAAG,KAAK,EACvC,OAA4C,EAC5C,QAAyC,EACzC,UAAqB,EACrB,EAAE;QACF,IAAI,eAAe,GAAuC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAC9E,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,MAAM,WAAW,GAAG;YAClB,OAAO,EAAE,OAAO,IAAI,EAAE;YACtB,QAAQ,EAAE,KAAK,EAAE,KAAa,EAAE,MAAgB,EAAE,EAAE;gBAClD,IAAI,QAAQ,EAAE,CAAC;oBACb,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;gBACtC,CAAC;gBACD,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC;oBAC5B,eAAe;oBACf,aAAa,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;iBAC9C,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE,EAAE,CAAC,CAAC;oBACvC,GAAG,WAAW;oBACd,0EAA0E;oBAC1E,GAAG,WAAW;iBACf,CAAC,CAAC,CAAC;YACN,CAAC;SACF,CAAC;QACF,OAAO,IAAA,2BAAkB,EAAC,WAAW,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,GAAG,UAAU,CAAC,CAAC;YAClD,MAAM,QAAQ,GAAG,MAAM,eAAe,CAAC;YACvC,QAAQ,GAAG,IAAI,CAAC;YAChB,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;gBAC7D,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;YAC5C,CAAC;YACD,OAAO,IAAA,+BAAsB,EAAC,EAAE,GAAG,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,aAAa,EAAE;gBACjF,OAAO;aACR,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,IAAI,WAAW,GAAwB,IAAI,CAAC,WAAW,CAAC;IACxD,IAAI,IAAI,EAAE,CAAC;QACT,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,OAAO,WAAW,KAAK,QAAQ,IAAI,WAAW,CAAC,UAAU,CAAC,qBAAqB,CAAC,EAAE,CAAC;YACrF,mDAAmD;YACnD,MAAM,QAAQ,GAAG,aAAa,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YACrD,WAAW,GAAG,MAAM,IAAA,oBAAW,EAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAC1D,CAAC;aAAM,IAAI,OAAO,EAAE,CAAC;YACnB,WAAW,GAAG,MAAM,IAAA,oBAAW,EAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAED,MAAM,QAAQ,GAAG,IAAA,sBAAc,EAAC,KAAK,CAAC,CAAC;IACvC,IAAI,QAAQ,EAAE,CAAC;QACb,IACE,CAAC,IAAI,CAAC,WAAW;YACjB,aAAa;YACb,CAAC,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAC3C,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;QACpF,CAAC;QAED,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;QAE3D,MAAM,SAAS,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;QAEzC,2BAA2B;QAC3B,MAAM,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,UAAU,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAE7F,iBAAiB;QACjB,MAAM,GAAG,GAAQ,UAAU,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;QAC9D,MAAM,EAAE,GAAG,SAAS,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;QAEhF,IAAI,CAAC,EAAE,EAAE,CAAC;YACR,MAAM,IAAI,KAAK,CACb,iCAAiC,QAAQ,aAAa,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAC3F,CAAC;QACJ,CAAC;QAED,OAAO,2BAA2B,CAAC,OAAO,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;IACxD,CAAC;IAED,mBAAmB;IACnB,OAAO,iBAAiB,CAAC,OAAO,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC;AACxD,CAAC;AAED,iDAAiD;AACjD,MAAM,aAAa,GAAG,CAAC,IAAY,EAAE,WAAmB,EAAE,EAAE;IAC1D,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE,CAAC,CAAC;IAC1C,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;IAChC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,IAAI,KAAK,IAAI;YAAE,SAAS;QAClD,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACxD,MAAM,OAAO,GAAG,UAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,CAC9C,CAAC,GAAG,EAAE,aAAa,EAAE,EAAE;YACrB,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC/C,GAAG,CAAC,GAAI,CAAC,WAAW,EAAE,CAAC,GAAG,KAAM,CAAC;YACjC,OAAO,GAAG,CAAC;QACb,CAAC,EACD,EAA4B,CAC7B,CAAC;QACF,MAAM,kBAAkB,GAAG,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAC1D,MAAM,SAAS,GAAG,gBAAgB,CAAC,IAAI,CAAC,kBAAmB,CAAC,CAAC;QAC7D,MAAM,aAAa,GAAG,oBAAoB,CAAC,IAAI,CAAC,kBAAmB,CAAC,CAAC;QACrE,IAAI,SAAS,EAAE,CAAC;YACd,MAAM,IAAI,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,aAAa,EAAE,CAAC;gBAClB,MAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;gBAClC,MAAM,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI,0BAA0B,CAAC;gBACnE,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,OAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC5C,QAAQ,CAAC,MAAM,CAAC,IAAK,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;YACzC,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,MAAM,CAAC,IAAK,EAAE,OAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1C,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,QAAQ,CAAC;AAClB,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,KAAK,EAAE,MAAsB,EAAmB,EAAE;IACvE,MAAM,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;IAClC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;IAClC,MAAM,IAAI,GAAa,EAAE,CAAC;IAC1B,IAAI,MAAyC,CAAC;IAC9C,GAAG,CAAC;QACF,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QAC7B,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,YAAY,UAAU,CAAC,EAAE,CAAC;gBAC1C,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC7C,CAAC;YACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE;IACvB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACvB,CAAC,CAAC", "sourcesContent": ["/**\n * Copyright © 2024 650 Industries.\n * Copyright © 2024 dai-shi.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * From waku https://github.com/dai-shi/waku/blob/32d52242c1450b5f5965860e671ff73c42da8bd0/packages/waku/src/lib/renderers/rsc-renderer.ts\n */\n\n// This file must remain platform agnostic for production exports.\n\n// Import the runtime to support polyfills for webpack to load modules in the server using Metro.\nimport '@expo/metro-runtime/rsc/runtime';\n\nimport type { ReactNode } from 'react';\nimport { renderToReadableStream, decodeReply } from 'react-server-dom-webpack/server';\n\nimport { fileURLToFilePath } from './path';\nimport { decodeActionId } from './router/utils';\nimport { runWithRenderStore, type EntriesDev, type EntriesPrd } from './server';\n\ndeclare namespace globalThis {\n  function __webpack_chunk_load__(id: string): Promise<unknown>;\n  function __webpack_require__(id: string): any;\n}\n\nexport interface RenderContext<T = unknown> {\n  rerender: (input: string, searchParams?: URLSearchParams) => void;\n  context: T;\n}\n\ntype ResolvedConfig = any;\n\nexport type RenderRscArgs = {\n  // TODO:\n  config: ResolvedConfig;\n\n  // Done\n  input: string;\n  context: Record<string, unknown> | undefined;\n  body?: ReadableStream | undefined;\n  contentType?: string | undefined;\n  decodedBody?: unknown;\n  moduleIdCallback?: (module: {\n    id: string;\n    chunks: string[];\n    name: string;\n    async: boolean;\n  }) => void;\n  onError?: (err: unknown) => void;\n};\n\ntype ResolveClientEntry = (id: string, server: boolean) => { id: string; chunks: string[] };\n\ntype RenderRscOpts = {\n  isExporting: boolean;\n  entries: EntriesDev;\n  resolveClientEntry: ResolveClientEntry;\n  loadServerModuleRsc: (url: string) => Promise<any>;\n};\n\nexport async function renderRsc(args: RenderRscArgs, opts: RenderRscOpts): Promise<ReadableStream> {\n  const { input, body, contentType, context, onError } = args;\n  const { resolveClientEntry, entries } = opts;\n\n  const {\n    default: { renderEntries },\n    // @ts-expect-error\n    buildConfig,\n  } = entries as (EntriesDev & { loadModule: never; buildConfig: never }) | EntriesPrd;\n\n  function resolveRequest(isServer: boolean, encodedId: string) {\n    const [\n      // File is the on-disk location of the module, this is injected during the \"use client\" transformation (babel).\n      file,\n      // The name of the import (e.g. \"default\" or \"\")\n      // This will be empty when using `module.exports = ` and `require('...')`.\n      name = '',\n    ] = encodedId.split('#') as [string, string];\n\n    const filePath = file.startsWith('file://') ? fileURLToFilePath(file) : file;\n\n    args.moduleIdCallback?.({\n      id: filePath,\n      chunks: [\n        // TODO: Add a lookup later which reads from the SSR manifest to get the correct chunk.\n        // NOTE(EvanBacon): This is a placeholder since we need to render RSC to get the client boundaries, which we then inject later.\n        'chunk:' + filePath,\n      ],\n      name,\n      async: true,\n    });\n    // We'll augment the file path with the incoming RSC request which will forward the metro props required to make a cache hit, e.g. platform=web&...\n    // This is similar to how we handle lazy bundling.\n    const resolved = resolveClientEntry(filePath, isServer);\n    return { id: resolved.id, chunks: resolved.chunks, name, async: true };\n  }\n\n  const bundlerConfig: Record<string, ReturnType<typeof resolveRequest>> = new Proxy(\n    {},\n    {\n      get(_target, encodedId: string) {\n        return resolveRequest(false, encodedId);\n      },\n    }\n  );\n\n  const serverConfig: Record<string, ReturnType<typeof resolveRequest>> = new Proxy(\n    {},\n    {\n      get(_target, encodedId: string) {\n        return resolveRequest(true, encodedId);\n      },\n    }\n  );\n\n  // @ts-ignore: Not part of global types. This is added to support server actions loading more actions.\n  global[`${__METRO_GLOBAL_PREFIX__}__loadBundleAsync`] = opts.loadServerModuleRsc;\n\n  const renderWithContext = async (\n    context: Record<string, unknown> | undefined,\n    input: string,\n    params: unknown\n  ) => {\n    const renderStore = {\n      context: context || {},\n      rerender: () => {\n        throw new Error('Cannot rerender');\n      },\n    };\n    return runWithRenderStore(renderStore, async () => {\n      const elements = await renderEntries(input, {\n        params,\n        buildConfig,\n      });\n      if (elements === null) {\n        const err = new Error('No function component found at: ' + input);\n        (err as any).statusCode = 404;\n        throw err;\n      }\n      if (Object.keys(elements).some((key) => key.startsWith('_'))) {\n        throw new Error('\"_\" prefix is reserved');\n      }\n      return renderToReadableStream(elements, bundlerConfig, {\n        onError,\n      });\n    });\n  };\n\n  const renderWithContextWithAction = async (\n    context: Record<string, unknown> | undefined,\n    actionFn: (...args: unknown[]) => unknown,\n    actionArgs: unknown[]\n  ) => {\n    let elementsPromise: Promise<Record<string, ReactNode>> = Promise.resolve({});\n    let rendered = false;\n    const renderStore = {\n      context: context || {},\n      rerender: async (input: string, params?: unknown) => {\n        if (rendered) {\n          throw new Error('already rendered');\n        }\n        elementsPromise = Promise.all([\n          elementsPromise,\n          renderEntries(input, { params, buildConfig }),\n        ]).then(([oldElements, newElements]) => ({\n          ...oldElements,\n          // FIXME we should actually check if newElements is null and send an error\n          ...newElements,\n        }));\n      },\n    };\n    return runWithRenderStore(renderStore, async () => {\n      const actionValue = await actionFn(...actionArgs);\n      const elements = await elementsPromise;\n      rendered = true;\n      if (Object.keys(elements).some((key) => key.startsWith('_'))) {\n        throw new Error('\"_\" prefix is reserved');\n      }\n      return renderToReadableStream({ ...elements, _value: actionValue }, bundlerConfig, {\n        onError,\n      });\n    });\n  };\n\n  let decodedBody: unknown | undefined = args.decodedBody;\n  if (body) {\n    const bodyStr = await streamToString(body);\n    if (typeof contentType === 'string' && contentType.startsWith('multipart/form-data')) {\n      // XXX This doesn't support streaming unlike busboy\n      const formData = parseFormData(bodyStr, contentType);\n      decodedBody = await decodeReply(formData, serverConfig);\n    } else if (bodyStr) {\n      decodedBody = await decodeReply(bodyStr, serverConfig);\n    }\n  }\n\n  const actionId = decodeActionId(input);\n  if (actionId) {\n    if (\n      !opts.isExporting &&\n      // @ts-ignore\n      !process.env.EXPO_UNSTABLE_SERVER_FUNCTIONS\n    ) {\n      throw new Error('Experimental support for React Server Functions is not enabled');\n    }\n\n    const args = Array.isArray(decodedBody) ? decodedBody : [];\n\n    const chunkInfo = serverConfig[actionId];\n\n    // Load module into memory.\n    await Promise.all(chunkInfo.chunks.map((chunk) => globalThis.__webpack_chunk_load__(chunk)));\n\n    // Import module.\n    const mod: any = globalThis.__webpack_require__(chunkInfo.id);\n    const fn = chunkInfo.name === '*' ? chunkInfo.name : mod[chunkInfo.name] || mod;\n\n    if (!fn) {\n      throw new Error(\n        `Could not find server action: ${actionId}. Module: ${JSON.stringify(chunkInfo, null, 2)}`\n      );\n    }\n\n    return renderWithContextWithAction(context, fn, args);\n  }\n\n  // method === 'GET'\n  return renderWithContext(context, input, decodedBody);\n}\n\n// TODO is this correct? better to use a library?\nconst parseFormData = (body: string, contentType: string) => {\n  const boundary = contentType.split('boundary=')[1];\n  const parts = body.split(`--${boundary}`);\n  const formData = new FormData();\n  for (const part of parts) {\n    if (part.trim() === '' || part === '--') continue;\n    const [rawHeaders, content] = part.split('\\r\\n\\r\\n', 2);\n    const headers = rawHeaders!.split('\\r\\n').reduce(\n      (acc, currentHeader) => {\n        const [key, value] = currentHeader.split(': ');\n        acc[key!.toLowerCase()] = value!;\n        return acc;\n      },\n      {} as Record<string, string>\n    );\n    const contentDisposition = headers['content-disposition'];\n    const nameMatch = /name=\"([^\"]+)\"/.exec(contentDisposition!);\n    const filenameMatch = /filename=\"([^\"]+)\"/.exec(contentDisposition!);\n    if (nameMatch) {\n      const name = nameMatch[1];\n      if (filenameMatch) {\n        const filename = filenameMatch[1];\n        const type = headers['content-type'] || 'application/octet-stream';\n        const blob = new Blob([content!], { type });\n        formData.append(name!, blob, filename);\n      } else {\n        formData.append(name!, content!.trim());\n      }\n    }\n  }\n  return formData;\n};\n\nconst streamToString = async (stream: ReadableStream): Promise<string> => {\n  const decoder = new TextDecoder();\n  const reader = stream.getReader();\n  const outs: string[] = [];\n  let result: ReadableStreamReadResult<unknown>;\n  do {\n    result = await reader.read();\n    if (result.value) {\n      if (!(result.value instanceof Uint8Array)) {\n        throw new Error('Unexepected buffer type');\n      }\n      outs.push(decoder.decode(result.value, { stream: true }));\n    }\n  } while (!result.done);\n  outs.push(decoder.decode());\n  return outs.join('');\n};\n"]}