{"version": 3, "file": "getReactNavigationConfig.js", "sourceRoot": "", "sources": ["../src/getReactNavigationConfig.ts"], "names": [], "mappings": ";;AAgCA,gDAcC;AAoCD,0EAOC;AAED,4DAYC;AAtGD,yCAA8C;AAW9C,sBAAsB;AACtB,mBAAmB;AACnB,SAAS,oCAAoC,CAAC,OAAe;IAC3D,wEAAwE;IACxE,IAAI,OAAO,KAAK,OAAO,EAAE,CAAC;QACxB,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,IAAI,OAAO,KAAK,YAAY,EAAE,CAAC;QAC7B,OAAO,YAAY,CAAC;IACtB,CAAC;IACD,MAAM,WAAW,GAAG,IAAA,2BAAgB,EAAC,OAAO,CAAC,CAAC;IAC9C,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;QACrC,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC;IAChC,CAAC;SAAM,IAAI,WAAW,EAAE,IAAI,EAAE,CAAC;QAC7B,OAAO,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC;IAChC,CAAC;SAAM,CAAC;QACN,OAAO,OAAO,CAAC;IACjB,CAAC;AACH,CAAC;AAED,SAAgB,kBAAkB,CAAC,QAAgB;IACjD,OAAO;IACL,gEAAgE;IAChE,yDAAyD;IACzD,qEAAqE;IACrE,QAAQ;SACL,KAAK,CAAC,GAAG,CAAC;QACX,qDAAqD;SACpD,GAAG,CAAC,oCAAoC,CAAC;QAC1C,sDAAsD;SACrD,MAAM,CAAC,OAAO,CAAC;QAChB,4BAA4B;SAC3B,IAAI,CAAC,GAAG,CAAC,CACb,CAAC;AACJ,CAAC;AAED,SAAS,wBAAwB,CAAC,IAAe,EAAE,QAAiB;IAClE,MAAM,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5C,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;QAC1B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;gBACL,IAAI;gBACJ,OAAO,EAAE,EAAE;gBACX,MAAM,EAAE,IAAI;aACb,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,OAAO,GAAG,+BAA+B,CAAC,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAEzE,MAAM,MAAM,GAAW;QACrB,IAAI;QACJ,OAAO;KACR,CAAC;IAEF,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC1B,mEAAmE;QACnE,kEAAkE;QAClE,6EAA6E;QAC7E,2CAA2C;QAC3C,MAAM,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;IAClD,CAAC;IAED,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAgB,+BAA+B,CAC7C,KAAkB,EAClB,QAAiB;IAEjB,OAAO,MAAM,CAAC,WAAW,CACvB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,wBAAwB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAU,CAAC,CACrF,CAAC;AACJ,CAAC;AAED,SAAgB,wBAAwB,CAAC,MAAiB,EAAE,QAAiB;IAC3E,MAAM,MAAM,GAAG;QACb,gBAAgB,EAAE,SAAS;QAC3B,OAAO,EAAE,+BAA+B,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC;KACpE,CAAC;IAEF,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAC5B,yFAAyF;QACzF,yEAAyE;QACzE,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAuB,CAAC;IAC3D,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC", "sourcesContent": ["import type { RouteNode } from './Route';\nimport { matchDynamicName } from './matchers';\n\nexport type Screen =\n  | string\n  | {\n      path: string;\n      screens: Record<string, Screen>;\n      _route?: RouteNode;\n      initialRouteName?: string;\n    };\n\n// `[page]` -> `:page`\n// `page` -> `page`\nfunction convertDynamicRouteToReactNavigation(segment: string): string {\n  // NOTE(EvanBacon): To support shared routes we preserve group segments.\n  if (segment === 'index') {\n    return '';\n  }\n  if (segment === '+not-found') {\n    return '*not-found';\n  }\n  const dynamicName = matchDynamicName(segment);\n  if (dynamicName && !dynamicName.deep) {\n    return `:${dynamicName.name}`;\n  } else if (dynamicName?.deep) {\n    return '*' + dynamicName.name;\n  } else {\n    return segment;\n  }\n}\n\nexport function parseRouteSegments(segments: string): string {\n  return (\n    // NOTE(EvanBacon): When there are nested routes without layouts\n    // the node.route will be something like `app/home/<USER>\n    // this needs to be split to ensure each segment is parsed correctly.\n    segments\n      .split('/')\n      // Convert each segment to a React Navigation format.\n      .map(convertDynamicRouteToReactNavigation)\n      // Remove any empty paths from groups or index routes.\n      .filter(Boolean)\n      // Join to return as a path.\n      .join('/')\n  );\n}\n\nfunction convertRouteNodeToScreen(node: RouteNode, metaOnly: boolean): Screen {\n  const path = parseRouteSegments(node.route);\n  if (!node.children.length) {\n    if (!metaOnly) {\n      return {\n        path,\n        screens: {},\n        _route: node,\n      };\n    }\n    return path;\n  }\n  const screens = getReactNavigationScreensConfig(node.children, metaOnly);\n\n  const screen: Screen = {\n    path,\n    screens,\n  };\n\n  if (node.initialRouteName) {\n    // NOTE(EvanBacon): This is bad because it forces all Layout Routes\n    // to be loaded into memory. We should move towards a system where\n    // the initial route name is either loaded asynchronously in the Layout Route\n    // or defined via a file system convention.\n    screen.initialRouteName = node.initialRouteName;\n  }\n\n  if (!metaOnly) {\n    screen._route = node;\n  }\n\n  return screen;\n}\n\nexport function getReactNavigationScreensConfig(\n  nodes: RouteNode[],\n  metaOnly: boolean\n): Record<string, Screen> {\n  return Object.fromEntries(\n    nodes.map((node) => [node.route, convertRouteNodeToScreen(node, metaOnly)] as const)\n  );\n}\n\nexport function getReactNavigationConfig(routes: RouteNode, metaOnly: boolean) {\n  const config = {\n    initialRouteName: undefined,\n    screens: getReactNavigationScreensConfig(routes.children, metaOnly),\n  };\n\n  if (routes.initialRouteName) {\n    // We're using LinkingOptions the generic type is `object` instead of a proper ParamList.\n    // So we need to cast the initialRouteName to `any` to avoid type errors.\n    config.initialRouteName = routes.initialRouteName as any;\n  }\n  return config;\n}\n"]}