{"version": 3, "file": "hooks.js", "sourceRoot": "", "sources": ["../src/hooks.ts"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;;;;AA4Bb,wDAIC;AAMD,8CAEC;AAMD,8DAEC;AAoBD,8BAEC;AAOD,sDAEC;AA2CD,kCAEC;AAmBD,kCAEC;AA4CD,sDAEC;AAyCD,oDA8BC;AAED,0CAsBC;AA5RD,qDAAyE;AACzE,kDAA0B;AAE1B,mCAAkD;AAClD,2CAAiD;AACjD,8DAAkE;AAIzD,6FAJO,2BAAY,OAIP;AAHrB,qDAAkD;AAKlD;;;;;;;;;;;;;;GAcG;AACH,SAAgB,sBAAsB;IACpC,OAAO,IAAA,sBAAa,GAAyC;SAC1D,SAAS,CAAC,8BAAkB,CAAE;SAC9B,QAAQ,EAAE,CAAC;AAChB,CAAC;AAED;;;GAGG;AACH,SAAgB,iBAAiB;IAC/B,OAAO,oBAAK,CAAC,aAAa,CAAC,OAAO,CAAC;AACrC,CAAC;AAED;;;GAGG;AACH,SAAgB,yBAAyB;IACvC,OAAO,oBAAK,CAAC,aAAa,CAAC;AAC7B,CAAC;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,SAAgB,SAAS;IACvB,OAAO,uBAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,SAAgB,qBAAqB;IACnC,OAAO,IAAA,2BAAY,GAAE,CAAC,mBAAmB,CAAC;AAC5C,CAAC;AA2CD,SAAgB,WAAW;IACzB,OAAO,IAAA,2BAAY,GAAE,CAAC,QAAQ,CAAC;AACjC,CAAC;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,SAAgB,WAAW;IACzB,OAAO,IAAA,2BAAY,GAAE,CAAC,QAAQ,CAAC;AACjC,CAAC;AA4CD,SAAgB,qBAAqB;IACnC,OAAO,IAAA,2BAAY,GAAE,CAAC,MAAM,CAAC;AAC/B,CAAC;AAyCD,SAAgB,oBAAoB;IAClC,MAAM,MAAM,GAAG,eAAK,CAAC,GAAG,CAAC,+BAAuB,CAAC,IAAI,EAAE,CAAC;IACxD,OAAO,MAAM,CAAC,WAAW,CACvB,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QAC1C,4GAA4G;QAC5G,sEAAsE;QACtE,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACxB,OAAO,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAC1B,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO;gBACL,GAAG;gBACH,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;oBACd,IAAI,CAAC;wBACH,OAAO,kBAAkB,CAAC,CAAC,CAAC,CAAC;oBAC/B,CAAC;oBAAC,MAAM,CAAC;wBACP,OAAO,CAAC,CAAC;oBACX,CAAC;gBACH,CAAC,CAAC;aACH,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC;gBACH,OAAO,CAAC,GAAG,EAAE,kBAAkB,CAAC,KAAe,CAAC,CAAC,CAAC;YACpD,CAAC;YAAC,MAAM,CAAC;gBACP,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACtB,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CACI,CAAC;AACX,CAAC;AAED,SAAgB,eAAe,CAAC,EAAE,MAAM,GAAG,KAAK,EAAE,GAAG,EAAE;IACrD,MAAM,SAAS,GAAG,eAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;IACvC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;QAC1C,IAAI,MAAM,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,CACV,iGAAiG,CAClG,CAAC;QACJ,CAAC;IACH,CAAC;IAED,sDAAsD;IACtD,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,qBAAqB,EAAE,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC;IACzE,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QAC9D,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,GAAG,KAAK,QAAQ;gBAAE,OAAO,EAAE,CAAC;YAChC,IAAI,GAAG,KAAK,QAAQ;gBAAE,OAAO,EAAE,CAAC;QAClC,CAAC;QAED,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAC5E,CAAC,CAAC,CAAC;IAEH,OAAO,IAAI,uBAAuB,CAAC,OAAO,CAAC,CAAC;AAC9C,CAAC;AAED,MAAM,uBAAwB,SAAQ,eAAe;IACnD,GAAG;QACD,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;IACzF,CAAC;IACD,MAAM;QACJ,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;IACzF,CAAC;IACD,MAAM;QACJ,MAAM,IAAI,KAAK,CAAC,qEAAqE,CAAC,CAAC;IACzF,CAAC;CACF", "sourcesContent": ["'use client';\n\nimport { NavigationProp, useNavigation } from '@react-navigation/native';\nimport React from 'react';\n\nimport { LocalRouteParamsContext } from './Route';\nimport { INTERNAL_SLOT_NAME } from './constants';\nimport { store, useRouteInfo } from './global-state/router-store';\nimport { router, Router } from './imperative-api';\nimport { RouteParams, RouteSegments, UnknownOutputParams, Route } from './types';\n\nexport { useRouteInfo };\n\n/**\n * Returns the [navigation state](https://reactnavigation.org/docs/navigation-state/)\n * of the navigator which contains the current screen.\n *\n * @example\n * ```tsx\n * import { useRootNavigationState } from 'expo-router';\n *\n * export default function Route() {\n *  const { routes } = useRootNavigationState();\n *\n *  return <Text>{routes[0].name}</Text>;\n * }\n * ```\n */\nexport function useRootNavigationState() {\n  return useNavigation<NavigationProp<object, never, string>>()\n    .getParent(INTERNAL_SLOT_NAME)!\n    .getState();\n}\n\n/**\n * @deprecated Use [`useNavigationContainerRef`](#usenavigationcontainerref) instead,\n * which returns a React `ref`.\n */\nexport function useRootNavigation() {\n  return store.navigationRef.current;\n}\n\n/**\n * @return The root `<NavigationContainer />` ref for the app. The `ref.current` may be `null`\n * if the `<NavigationContainer />` hasn't mounted yet.\n */\nexport function useNavigationContainerRef() {\n  return store.navigationRef;\n}\n\n/**\n *\n * Returns the [Router](#router) object for imperative navigation.\n *\n * @example\n *```tsx\n * import { useRouter } from 'expo-router';\n * import { Text } from 'react-native';\n *\n * export default function Route() {\n *  const router = useRouter();\n *\n *  return (\n *   <Text onPress={() => router.push('/home')}>Go Home</Text>\n *  );\n *}\n * ```\n */\nexport function useRouter(): Router {\n  return router;\n}\n\n/**\n * @private\n * @returns The current global pathname with query params attached. This may change in the future to include the hostname\n * from a predefined universal link. For example, `/foobar?hey=world` becomes `https://acme.dev/foobar?hey=world`.\n */\nexport function useUnstableGlobalHref(): string {\n  return useRouteInfo().unstable_globalHref;\n}\n\n/**\n * Returns a list of selected file segments for the currently selected route. Segments are not normalized,\n * so they will be the same as the file path. For example, `/[id]?id=normal` becomes `[\"[id]\"]`.\n *\n * @example\n * ```tsx app/profile/[user].tsx\n * import { Text } from 'react-native';\n * import { useSegments } from 'expo-router';\n *\n * export default function Route() {\n *   // segments = [\"profile\", \"[user]\"]\n *   const segments = useSegments();\n *\n *   return <Text>Hello</Text>;\n * }\n * ```\n *\n *\n * `useSegments` can be typed using an abstract. Consider the following file structure:\n *\n * ```md\n * - app\n *   - [user]\n *     - index.tsx\n *     - followers.tsx\n *   - settings.tsx\n * ```\n *\n *\n * This can be strictly typed using the following abstract with `useSegments` hook:\n *\n * ```tsx\n * const [first, second] = useSegments<['settings'] | ['[user]'] | ['[user]', 'followers']>()\n * ```\n */\nexport function useSegments<TSegments extends Route = Route>(): RouteSegments<TSegments>;\n\n/**\n *  @hidden\n */\nexport function useSegments<TSegments extends RouteSegments<Route>>(): TSegments;\nexport function useSegments() {\n  return useRouteInfo().segments;\n}\n\n/**\n * Returns the currently selected route location without search parameters. For example, `/acme?foo=bar` returns `/acme`.\n * Segments will be normalized. For example, `/[id]?id=normal` becomes `/normal`.\n *\n * @example\n * ```tsx app/profile/[user].tsx\n * import { Text } from 'react-native';\n * import { usePathname } from 'expo-router';\n *\n * export default function Route() {\n *   // pathname = \"/profile/baconbrix\"\n *   const pathname = usePathname();\n *\n *   return <Text>User: {user}</Text>;\n * }\n * ```\n */\nexport function usePathname(): string {\n  return useRouteInfo().pathname;\n}\n\n/**\n * @hidden\n */\nexport function useGlobalSearchParams<\n  TParams extends UnknownOutputParams = UnknownOutputParams,\n>(): TParams;\n\n/**\n * @hidden\n */\nexport function useGlobalSearchParams<TRoute extends Route>(): RouteParams<TRoute>;\n\n/**\n * Returns URL parameters for globally selected route, including dynamic path segments.\n * This function updates even when the route is not focused. Useful for analytics or\n * other background operations that don't draw to the screen.\n *\n * Route URL example: `acme://profile/baconbrix?extra=info`.\n *\n * When querying search params in a stack, opt-towards using\n * [`useLocalSearchParams`](#uselocalsearchparams) because it will only update when the route is focused.\n *\n * > **Note:** For usage information, see\n * [Local versus global search parameters](/router/reference/url-parameters/#local-versus-global-url-parameters).\n *\n * @example\n * ```tsx app/profile/[user].tsx\n * import { Text } from 'react-native';\n * import { useGlobalSearchParams } from 'expo-router';\n *\n * export default function Route() {\n *   // user=baconbrix & extra=info\n *   const { user, extra } = useGlobalSearchParams();\n *\n *   return <Text>User: {user}</Text>;\n * }\n * ```\n */\nexport function useGlobalSearchParams<\n  TRoute extends Route,\n  TParams extends UnknownOutputParams = UnknownOutputParams,\n>(): RouteParams<TRoute> & TParams;\nexport function useGlobalSearchParams() {\n  return useRouteInfo().params;\n}\n\n/**\n * @hidden\n */\nexport function useLocalSearchParams<\n  TParams extends UnknownOutputParams = UnknownOutputParams,\n>(): TParams;\n\n/**\n * @hidden\n */\nexport function useLocalSearchParams<TRoute extends Route>(): RouteParams<TRoute>;\n\n/**\n * Returns the URL parameters for the contextually focused route. Useful for stacks where you may push a new screen\n * that changes the query parameters.  For dynamic routes, both the route parameters and the search parameters are returned.\n *\n * Route URL example: `acme://profile/baconbrix?extra=info`.\n *\n * To observe updates even when the invoking route is not focused, use [`useGlobalSearchParams`](#useglobalsearchparams).\n *\n * > **Note:** For usage information, see\n * [Local versus global search parameters](/router/reference/url-parameters/#local-versus-global-url-parameters).\n *\n * @example\n * ```tsx app/profile/[user].tsx\n * import { Text } from 'react-native';\n * import { useLocalSearchParams } from 'expo-router';\n *\n * export default function Route() {\n *  // user=baconbrix & extra=info\n *  const { user, extra } = useLocalSearchParams();\n *\n *  return <Text>User: {user}</Text>;\n * }\n */\nexport function useLocalSearchParams<\n  TRoute extends Route,\n  TParams extends UnknownOutputParams = UnknownOutputParams,\n>(): RouteParams<TRoute> & TParams;\nexport function useLocalSearchParams() {\n  const params = React.use(LocalRouteParamsContext) ?? {};\n  return Object.fromEntries(\n    Object.entries(params).map(([key, value]) => {\n      // React Navigation doesn't remove \"undefined\" values from the params object, and you cannot remove them via\n      // navigation.setParams as it shallow merges. Hence, we hide them here\n      if (value === undefined) {\n        return [key, undefined];\n      }\n\n      if (Array.isArray(value)) {\n        return [\n          key,\n          value.map((v) => {\n            try {\n              return decodeURIComponent(v);\n            } catch {\n              return v;\n            }\n          }),\n        ];\n      } else {\n        try {\n          return [key, decodeURIComponent(value as string)];\n        } catch {\n          return [key, value];\n        }\n      }\n    })\n  ) as any;\n}\n\nexport function useSearchParams({ global = false } = {}): URLSearchParams {\n  const globalRef = React.useRef(global);\n  if (process.env.NODE_ENV !== 'production') {\n    if (global !== globalRef.current) {\n      console.warn(\n        `Detected change in 'global' option of useSearchParams. This value cannot change between renders`\n      );\n    }\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  const params = global ? useGlobalSearchParams() : useLocalSearchParams();\n  const entries = Object.entries(params).flatMap(([key, value]) => {\n    if (global) {\n      if (key === 'params') return [];\n      if (key === 'screen') return [];\n    }\n\n    return Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]];\n  });\n\n  return new ReadOnlyURLSearchParams(entries);\n}\n\nclass ReadOnlyURLSearchParams extends URLSearchParams {\n  set() {\n    throw new Error('The URLSearchParams object return from useSearchParams is read-only');\n  }\n  append() {\n    throw new Error('The URLSearchParams object return from useSearchParams is read-only');\n  }\n  delete() {\n    throw new Error('The URLSearchParams object return from useSearchParams is read-only');\n  }\n}\n"]}