{"version": 3, "file": "getServerManifest.js", "sourceRoot": "", "sources": ["../src/getServerManifest.ts"], "names": [], "mappings": ";;AAqFA,8CA4EC;AA4JD,wCAgBC;AAnUD,yCAA2D;AAC3D,6CAA0C;AAyD1C,SAAS,eAAe,CAAC,KAAgB;IACvC,OAAO,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC;AAC3E,CAAC;AAED,SAAS,QAAQ,CAAI,GAAQ,EAAE,GAAwB;IACrD,MAAM,IAAI,GAAG,IAAI,GAAG,EAAU,CAAC;IAC/B,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;QACzB,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;QACrB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC;YACjB,OAAO,KAAK,CAAC;QACf,CAAC;QACD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC;AAED,yFAAyF;AACzF,SAAgB,iBAAiB,CAAC,KAAgB;IAChD,SAAS,YAAY,CAAC,KAAgB,EAAE,cAAsB,EAAE;QAC9D,kGAAkG;QAClG,MAAM,aAAa,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAE3E,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YAC1B,OAAO,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAClF,CAAC;QAED,0FAA0F;QAC1F,mGAAmG;QACnG,qGAAqG;QACrG,IAAI,GAAW,CAAC;QAChB,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,GAAG,GAAG,IAAA,wBAAa,EAAC,KAAK,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC;QACvE,CAAC;aAAM,CAAC;YACN,GAAG,GAAG,IAAA,wBAAa,EAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC;QACpE,CAAC;QACD,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED,0EAA0E;IAC1E,MAAM,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC;SAC7B,IAAI,CAAC,CAAC,CAAC,EAAE,AAAD,EAAG,CAAC,CAAC,EAAE,CAAC,EAAE,AAAD,EAAG,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,uBAAU,EAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SAC5C,OAAO,EAAE,CAAC;IAEb,MAAM,SAAS,GAAG,QAAQ,CACxB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,AAAD,EAAG,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,EAClD,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CACjB,CAAC;IAEF,MAAM,WAAW,GAAG,QAAQ,CAC1B,IAAI,CAAC,MAAM,CACT,CAAC,CAAC,EAAE,AAAD,EAAG,KAAK,CAAC,EAAE,EAAE,CACd,KAAK,CAAC,IAAI,KAAK,OAAO;QACtB,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,SAAS,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAC/F,EACD,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CACjB,CAAC;IAEF,MAAM,SAAS,GAAG,QAAQ,CACxB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,AAAD,EAAG,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,EACvD,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CACjB;SACE,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;QAChB,QAAQ,CAAC,CAAC,CAAC;YACT,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,AAAD,EAAG,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC;gBACvF,GAAG,CAAC;QAEN,OAAO,QAAQ,CAAC;IAClB,CAAC,CAAC;SACD,OAAO,EAAE,CAAC;IAEb,MAAM,QAAQ,GAAG,QAAQ,CACvB,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,AAAD,EAAG,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,SAAS,CAAC,EACtD,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CACjB;SACE,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;QACf,OAAO,CAAC,CAAC,CAAC;YACR,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,AAAD,EAAG,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,UAAU,KAAK,OAAO,CAAC,CAAC,CAAC,CAAC,qBAAqB,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtF,GAAG,CAAC;QAEN,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC;SACD,OAAO,EAAE,CAAC;IAEb,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,AAAD,EAAG,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;IACpF,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,AAAD,EAAG,KAAK,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;IAEnF,OAAO;QACL,SAAS,EAAE,4BAA4B,CAAC,SAAS,CAAC;QAClD,UAAU,EAAE,4BAA4B,CAAC,cAAc,CAAC;QACxD,cAAc,EAAE,4BAA4B,CAAC,cAAc,CAAC;QAC5D,SAAS,EAAE,4BAA4B,CAAC,SAAS,CAAC;QAClD,QAAQ,EAAE,4BAA4B,CAAC,QAAQ,CAAC;KACjD,CAAC;AACJ,CAAC;AAED,SAAS,4BAA4B,CACnC,KAAoC;IAEpC,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,mBAAmB,EAAE,aAAa,EAAE,IAAI,CAAC,EAAE,EAAE;QAC9D,MAAM,OAAO,GAAoC,kBAAkB,CACjE,mBAAmB,EACnB,aAAa,EACb,IAAI,CAAC,UAAU,CAChB,CAAC;QACF,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;QAC3B,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;QAC3B,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QACjC,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,kBAAkB,CACzB,eAAuB,EACvB,IAAY,EACZ,IAAY;IAEZ,MAAM,MAAM,GAAG,yBAAyB,CAAC,eAAe,CAAC,CAAC;IAC1D,OAAO;QACL,IAAI;QACJ,IAAI;QACJ,UAAU,EAAE,IAAI,MAAM,CAAC,uBAAuB,SAAS;QACvD,SAAS,EAAE,MAAM,CAAC,SAAS;KAC5B,CAAC;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,oBAAoB;IAC3B,IAAI,eAAe,GAAG,EAAE,CAAC,CAAC,8DAA8D;IACxF,IAAI,aAAa,GAAG,CAAC,CAAC;IAEtB,OAAO,GAAG,EAAE;QACV,IAAI,MAAM,GAAG,EAAE,CAAC;QAChB,IAAI,aAAa,GAAG,IAAI,CAAC;QAEzB,8CAA8C;QAC9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,IAAI,aAAa,EAAE,CAAC;gBAClB,eAAe,EAAE,CAAC;gBAClB,IAAI,eAAe,GAAG,GAAG,EAAE,CAAC;oBAC1B,eAAe,GAAG,EAAE,CAAC,CAAC,eAAe;oBACrC,aAAa,GAAG,IAAI,CAAC,CAAC,2CAA2C;gBACnE,CAAC;qBAAM,CAAC;oBACN,aAAa,GAAG,KAAK,CAAC;gBACxB,CAAC;YACH,CAAC;YACD,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC,GAAG,MAAM,CAAC;QACzD,CAAC;QAED,4DAA4D;QAC5D,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,EAAE,CAAC;YAChB,eAAe,GAAG,EAAE,CAAC,CAAC,6CAA6C;QACrE,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAa;IACxC,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC;AACzC,CAAC;AAED,SAAS,yBAAyB,CAAC,KAAa;IAC9C,MAAM,QAAQ,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAChE,MAAM,eAAe,GAAG,oBAAoB,EAAE,CAAC;IAC/C,MAAM,SAAS,GAA2B,EAAE,CAAC;IAC7C,OAAO;QACL,uBAAuB,EAAE,QAAQ;aAC9B,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;YACtB,IAAI,OAAO,KAAK,YAAY,IAAI,KAAK,KAAK,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9D,OAAO,GAAG,gBAAgB,CAAC;YAC7B,CAAC;YACD,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC,OAAO,CAAC,CAAC;gBAC3D,uDAAuD;gBACvD,kBAAkB;gBAClB,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;gBACzC,IAAI,UAAU,GAAG,KAAK,CAAC;gBAEvB,kEAAkE;gBAClE,WAAW;gBACX,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;oBACtD,UAAU,GAAG,IAAI,CAAC;gBACpB,CAAC;gBACD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;oBACjD,UAAU,GAAG,IAAI,CAAC;gBACpB,CAAC;gBAED,8CAA8C;gBAC9C,IAAI,UAAU,IAAI,SAAS,EAAE,CAAC;oBAC5B,UAAU,GAAG,IAAI,CAAC;gBACpB,CAAC;gBAED,IAAI,UAAU,EAAE,CAAC;oBACf,UAAU,GAAG,eAAe,EAAE,CAAC;gBACjC,CAAC;gBAED,SAAS,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;gBAC7B,OAAO,MAAM;oBACX,CAAC,CAAC,QAAQ;wBACR,CAAC,CAAC,UAAU,UAAU,SAAS;wBAC/B,CAAC,CAAC,OAAO,UAAU,OAAO;oBAC5B,CAAC,CAAC,OAAO,UAAU,UAAU,CAAC;YAClC,CAAC;iBAAM,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpC,MAAM,SAAS,GAAG,IAAA,yBAAc,EAAC,OAAO,CAAE;qBACvC,KAAK,CAAC,GAAG,CAAC;qBACV,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;qBAC5B,MAAM,CAAC,OAAO,CAAC,CAAC;gBACnB,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACzB,MAAM,eAAe,GAAG,SAAS,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;oBACnF,wBAAwB;oBACxB,OAAO,OAAO,eAAe,IAAI,CAAC;gBACpC,CAAC;qBAAM,CAAC;oBACN,sCAAsC;oBACtC,OAAO,OAAO,kBAAkB,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChD,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,IAAI,kBAAkB,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3C,CAAC;QACH,CAAC,CAAC;aACD,IAAI,CAAC,EAAE,CAAC;QACX,SAAS;KACV,CAAC;AACJ,CAAC;AAED,0EAA0E;AAC1E,MAAM,WAAW,GAAG,qBAAqB,CAAC;AAC1C,MAAM,eAAe,GAAG,sBAAsB,CAAC;AAE/C,SAAS,kBAAkB,CAAC,GAAW;IACrC,+GAA+G;IAC/G,IAAI,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QAC1B,OAAO,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;IAC9C,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAgB,cAAc,CAAC,KAAa;IAC1C,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,IAAI,GAAG,KAAK,CAAC;IAEjB,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1B,QAAQ,GAAG,IAAI,CAAC;QAChB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC;IAED,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACzB,MAAM,GAAG,IAAI,CAAC;QACd,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACvB,CAAC;IAED,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;AACpC,CAAC", "sourcesContent": ["/**\n * Copyright © 2023 650 Industries.\n * Copyright © 2023 Vercel, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * Based on https://github.com/vercel/next.js/blob/1df2686bc9964f1a86c444701fa5cbf178669833/packages/next/src/shared/lib/router/utils/route-regex.ts\n */\nimport type { RouteNode } from './Route';\nimport { getContextKey, matchGroupName } from './matchers';\nimport { sortRoutes } from './sortRoutes';\n\n// TODO: Share these types across cli, server, router, etc.\nexport type ExpoRouterServerManifestV1Route<TRegex = string> = {\n  file: string;\n  page: string;\n  /**\n   * Keys are route param names that have been normalized for a regex named-matcher, values are the original route param names.\n   */\n  routeKeys: Record<string, string>;\n  /**\n   * Regex for matching a path against the route.\n   * The regex is normalized for named matchers so keys must be looked up against the `routeKeys` object to collect the original route param names.\n   * Regex matching alone cannot accurately route to a file, the order in which routes are matched is equally important to ensure correct priority.\n   */\n  namedRegex: TRegex;\n  /** Indicates that the route was generated and does not map to any file in the project's routes directory. */\n  generated?: boolean;\n  /** Indicates that this is a redirect that should use 301 instead of 307 */\n  permanent?: boolean;\n  /** If a redirect, which methods are allowed. Undefined represents all methods */\n  methods?: string[];\n};\n\nexport type ExpoRouterServerManifestV1<TRegex = string> = {\n  /**\n   * Rewrites. These occur first\n   */\n  rewrites: ExpoRouterServerManifestV1Route<TRegex>[];\n  /**\n   * List of routes that match second. Returns 301 and redirects to another path.\n   */\n  redirects: ExpoRouterServerManifestV1Route<TRegex>[];\n  /**\n   * Routes that return static HTML files for a given path.\n   * These are only matched against requests with method `GET` and `HEAD`.\n   */\n  htmlRoutes: ExpoRouterServerManifestV1Route<TRegex>[];\n  /**\n   * Routes that are matched after HTML routes and invoke WinterCG-compliant functions.\n   */\n  apiRoutes: ExpoRouterServerManifestV1Route<TRegex>[];\n  /** List of routes that are matched last and return with status code 404. */\n  notFoundRoutes: ExpoRouterServerManifestV1Route<TRegex>[];\n};\n\nexport interface Group {\n  pos: number;\n  repeat: boolean;\n  optional: boolean;\n}\n\nexport interface RouteRegex {\n  groups: Record<string, Group>;\n  re: RegExp;\n}\n\nfunction isNotFoundRoute(route: RouteNode) {\n  return route.dynamic && route.dynamic[route.dynamic.length - 1].notFound;\n}\n\nfunction uniqueBy<T>(arr: T[], key: (item: T) => string): T[] {\n  const seen = new Set<string>();\n  return arr.filter((item) => {\n    const id = key(item);\n    if (seen.has(id)) {\n      return false;\n    }\n    seen.add(id);\n    return true;\n  });\n}\n\n// Given a nested route tree, return a flattened array of all routes that can be matched.\nexport function getServerManifest(route: RouteNode): ExpoRouterServerManifestV1 {\n  function getFlatNodes(route: RouteNode, parentRoute: string = ''): [string, string, RouteNode][] {\n    // Use a recreated route instead of contextKey because we duplicate nodes to support array syntax.\n    const absoluteRoute = [parentRoute, route.route].filter(Boolean).join('/');\n\n    if (route.children.length) {\n      return route.children.map((child) => getFlatNodes(child, absoluteRoute)).flat();\n    }\n\n    // API Routes are handled differently to HTML routes because they have no nested behavior.\n    // An HTML route can be different based on parent segments due to layout routes, therefore multiple\n    // copies should be rendered. However, an API route is always the same regardless of parent segments.\n    let key: string;\n    if (route.type.includes('api')) {\n      key = getContextKey(route.contextKey).replace(/\\/index$/, '') ?? '/';\n    } else {\n      key = getContextKey(absoluteRoute).replace(/\\/index$/, '') ?? '/';\n    }\n    return [[key, '/' + absoluteRoute, route]];\n  }\n\n  // Remove duplicates from the runtime manifest which expands array syntax.\n  const flat = getFlatNodes(route)\n    .sort(([, , a], [, , b]) => sortRoutes(b, a))\n    .reverse();\n\n  const apiRoutes = uniqueBy(\n    flat.filter(([, , route]) => route.type === 'api'),\n    ([path]) => path\n  );\n\n  const otherRoutes = uniqueBy(\n    flat.filter(\n      ([, , route]) =>\n        route.type === 'route' ||\n        (route.type === 'rewrite' && (route.methods === undefined || route.methods.includes('GET')))\n    ),\n    ([path]) => path\n  );\n\n  const redirects = uniqueBy(\n    flat.filter(([, , route]) => route.type === 'redirect'),\n    ([path]) => path\n  )\n    .map((redirect) => {\n      redirect[1] =\n        flat.find(([, , route]) => route.contextKey === redirect[2].destinationContextKey)?.[0] ??\n        '/';\n\n      return redirect;\n    })\n    .reverse();\n\n  const rewrites = uniqueBy(\n    flat.filter(([, , route]) => route.type === 'rewrite'),\n    ([path]) => path\n  )\n    .map((rewrite) => {\n      rewrite[1] =\n        flat.find(([, , route]) => route.contextKey === rewrite[2].destinationContextKey)?.[0] ??\n        '/';\n\n      return rewrite;\n    })\n    .reverse();\n\n  const standardRoutes = otherRoutes.filter(([, , route]) => !isNotFoundRoute(route));\n  const notFoundRoutes = otherRoutes.filter(([, , route]) => isNotFoundRoute(route));\n\n  return {\n    apiRoutes: getMatchableManifestForPaths(apiRoutes),\n    htmlRoutes: getMatchableManifestForPaths(standardRoutes),\n    notFoundRoutes: getMatchableManifestForPaths(notFoundRoutes),\n    redirects: getMatchableManifestForPaths(redirects),\n    rewrites: getMatchableManifestForPaths(rewrites),\n  };\n}\n\nfunction getMatchableManifestForPaths(\n  paths: [string, string, RouteNode][]\n): ExpoRouterServerManifestV1Route[] {\n  return paths.map(([normalizedRoutePath, absoluteRoute, node]) => {\n    const matcher: ExpoRouterServerManifestV1Route = getNamedRouteRegex(\n      normalizedRoutePath,\n      absoluteRoute,\n      node.contextKey\n    );\n    if (node.generated) {\n      matcher.generated = true;\n    }\n\n    if (node.permanent) {\n      matcher.permanent = true;\n    }\n\n    if (node.methods) {\n      matcher.methods = node.methods;\n    }\n\n    return matcher;\n  });\n}\n\nfunction getNamedRouteRegex(\n  normalizedRoute: string,\n  page: string,\n  file: string\n): ExpoRouterServerManifestV1Route {\n  const result = getNamedParametrizedRoute(normalizedRoute);\n  return {\n    file,\n    page,\n    namedRegex: `^${result.namedParameterizedRoute}(?:/)?$`,\n    routeKeys: result.routeKeys,\n  };\n}\n\n/**\n * Builds a function to generate a minimal routeKey using only a-z and minimal\n * number of characters.\n */\nfunction buildGetSafeRouteKey() {\n  let currentCharCode = 96; // Starting one before 'a' to make the increment logic simpler\n  let currentLength = 1;\n\n  return () => {\n    let result = '';\n    let incrementNext = true;\n\n    // Iterate from right to left to build the key\n    for (let i = 0; i < currentLength; i++) {\n      if (incrementNext) {\n        currentCharCode++;\n        if (currentCharCode > 122) {\n          currentCharCode = 97; // Reset to 'a'\n          incrementNext = true; // Continue to increment the next character\n        } else {\n          incrementNext = false;\n        }\n      }\n      result = String.fromCharCode(currentCharCode) + result;\n    }\n\n    // If all characters are 'z', increase the length of the key\n    if (incrementNext) {\n      currentLength++;\n      currentCharCode = 96; // This will make the next key start with 'a'\n    }\n\n    return result;\n  };\n}\n\nfunction removeTrailingSlash(route: string): string {\n  return route.replace(/\\/$/, '') || '/';\n}\n\nfunction getNamedParametrizedRoute(route: string) {\n  const segments = removeTrailingSlash(route).slice(1).split('/');\n  const getSafeRouteKey = buildGetSafeRouteKey();\n  const routeKeys: Record<string, string> = {};\n  return {\n    namedParameterizedRoute: segments\n      .map((segment, index) => {\n        if (segment === '+not-found' && index === segments.length - 1) {\n          segment = '[...not-found]';\n        }\n        if (/^\\[.*\\]$/.test(segment)) {\n          const { name, optional, repeat } = parseParameter(segment);\n          // replace any non-word characters since they can break\n          // the named regex\n          let cleanedKey = name.replace(/\\W/g, '');\n          let invalidKey = false;\n\n          // check if the key is still invalid and fallback to using a known\n          // safe key\n          if (cleanedKey.length === 0 || cleanedKey.length > 30) {\n            invalidKey = true;\n          }\n          if (!isNaN(parseInt(cleanedKey.slice(0, 1), 10))) {\n            invalidKey = true;\n          }\n\n          // Prevent duplicates after sanitizing the key\n          if (cleanedKey in routeKeys) {\n            invalidKey = true;\n          }\n\n          if (invalidKey) {\n            cleanedKey = getSafeRouteKey();\n          }\n\n          routeKeys[cleanedKey] = name;\n          return repeat\n            ? optional\n              ? `(?:/(?<${cleanedKey}>.+?))?`\n              : `/(?<${cleanedKey}>.+?)`\n            : `/(?<${cleanedKey}>[^/]+?)`;\n        } else if (/^\\(.*\\)$/.test(segment)) {\n          const groupName = matchGroupName(segment)!\n            .split(',')\n            .map((group) => group.trim())\n            .filter(Boolean);\n          if (groupName.length > 1) {\n            const optionalSegment = `\\\\((?:${groupName.map(escapeStringRegexp).join('|')})\\\\)`;\n            // Make section optional\n            return `(?:/${optionalSegment})?`;\n          } else {\n            // Use simpler regex for single groups\n            return `(?:/${escapeStringRegexp(segment)})?`;\n          }\n        } else {\n          return `/${escapeStringRegexp(segment)}`;\n        }\n      })\n      .join(''),\n    routeKeys,\n  };\n}\n\n// regexp is based on https://github.com/sindresorhus/escape-string-regexp\nconst reHasRegExp = /[|\\\\{}()[\\]^$+*?.-]/;\nconst reReplaceRegExp = /[|\\\\{}()[\\]^$+*?.-]/g;\n\nfunction escapeStringRegexp(str: string) {\n  // see also: https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/escapeRegExp.js#L23\n  if (reHasRegExp.test(str)) {\n    return str.replace(reReplaceRegExp, '\\\\$&');\n  }\n  return str;\n}\n\nexport function parseParameter(param: string) {\n  let repeat = false;\n  let optional = false;\n  let name = param;\n\n  if (/^\\[.*\\]$/.test(name)) {\n    optional = true;\n    name = name.slice(1, -1);\n  }\n\n  if (/^\\.\\.\\./.test(name)) {\n    repeat = true;\n    name = name.slice(3);\n  }\n\n  return { name, repeat, optional };\n}\n"]}