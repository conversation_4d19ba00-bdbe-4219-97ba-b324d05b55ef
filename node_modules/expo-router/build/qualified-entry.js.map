{"version": 3, "file": "qualified-entry.js", "sourceRoot": "", "sources": ["../src/qualified-entry.tsx"], "names": [], "mappings": ";AAAA,wFAAwF;AACxF,yFAAyF;AACzF,6FAA6F;AAC7F,2FAA2F;;;;;AAa3F,kBAMC;AAjBD,iFAAiF;AACjF,yEAAyE;AACzE,2CAAuC;AACvC,kDAA0B;AAE1B,yCAAsC;AACtC,iCAA8B;AAE9B,0BAAwB;AAExB,4DAA4D;AAC5D,SAAgB,GAAG;IACjB,OAAO,CACL,CAAC,WAAI,CAAC,QAAQ,CACZ;MAAA,CAAC,mBAAQ,CAAC,OAAO,CAAC,CAAC,UAAG,CAAC,EACzB;IAAA,EAAE,WAAI,CAAC,QAAQ,CAAC,CACjB,CAAC;AACJ,CAAC", "sourcesContent": ["// The entry component (one that uses context modules) cannot be in the same file as the\n// entry side-effects, otherwise they'll be updated when files are added/removed from the\n// app directory. This will cause a lot of unfortunate errors regarding HMR and Fast Refresh.\n// This is because Fast Refresh is sending the entire file containing an updated component.\n\n// This has to be the string \"expo-router/_ctx\" as we resolve the exact string to\n// a different file in a custom resolver for bundle splitting in Node.js.\nimport { ctx } from 'expo-router/_ctx';\nimport React from 'react';\n\nimport { ExpoRoot } from './ExpoRoot';\nimport { Head } from './head';\n\nimport './fast-refresh';\n\n// Must be exported or Fast Refresh won't update the context\nexport function App() {\n  return (\n    <Head.Provider>\n      <ExpoRoot context={ctx} />\n    </Head.Provider>\n  );\n}\n"]}