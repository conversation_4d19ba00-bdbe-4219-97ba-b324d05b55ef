{"version": 3, "file": "Tabs.js", "sourceRoot": "", "sources": ["../../src/ui/Tabs.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAmFA,oBAsBC;AA2BD,kDAGC;AAgBD,kDAiEC;AAxND,qDAQkC;AAClC,iCAUe;AACf,+CAA2D;AAE3D,6CAKsB;AACtB,uCAAsC;AACtC,2CAAkE;AAClE,uCAAsC;AACtC,6CAA4C;AAC5C,qCAA8E;AAC9E,iDAA8C;AAC9C,oCAAuD;AACvD,oCAAwC;AACxC,uCAA2C;AAC3C,sCAAoD;AACpD,kDAA6E;AAE7E,+CAA6B;AAC7B,4CAA0B;AAC1B,4CAA0B;AAC1B,+CAA6B;AA0B7B;;;;;;;;;;;;;GAaG;AACH,SAAgB,IAAI,CAAC,KAAgB;IACnC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,IAAI,EAAE,GAAG,KAAK,CAAC;IACtD,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,yBAAgB,CAAC,CAAC,CAAC,mBAAI,CAAC;IAE/C,MAAM,EAAE,iBAAiB,EAAE,GAAG,mBAAmB,CAAC;QAChD,0EAA0E;QAC1E,QAAQ,EACN,OAAO;YACP,IAAA,sBAAc,EAAC,QAAQ,CAAC;YACxB,QAAQ,CAAC,KAAK;YACd,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ;YAClC,UAAU,IAAI,QAAQ,CAAC,KAAK;YAC1B,CAAC,CAAE,QAAQ,CAAC,KAAK,CAAC,QAAsB;YACxC,CAAC,CAAC,QAAQ;QACd,GAAG,OAAO;KACX,CAAC,CAAC;IAEH,OAAO,CACL,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,IAAI,IAAI,CAAC,CACrC;MAAA,CAAC,iBAAiB,CAAC,CAAC,QAAQ,CAAC,EAAE,iBAAiB,CAClD;IAAA,EAAE,IAAI,CAAC,CACR,CAAC;AACJ,CAAC;AAUD;;;;;;;;;;;;;;;;GAgBG;AACH,SAAgB,mBAAmB,CAAC,OAAmC;IACrE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;IACtC,OAAO,mBAAmB,CAAC,EAAE,QAAQ,EAAE,yBAAyB,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC;AACzF,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,SAAgB,mBAAmB,CAAC,OAAmC;IACrE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;IACtC,uEAAuE;IACvE,MAAM,gBAAgB,GAAG,IAAA,WAAG,EAAC,iCAAoB,CAAC,CAAC;IACnD,MAAM,SAAS,GAAG,IAAA,oBAAY,GAAE,CAAC;IACjC,MAAM,UAAU,GAAG,IAAA,qBAAa,GAAE,CAAC;IACnC,MAAM,OAAO,GAAG,IAAA,WAAG,EAAC,uBAAc,CAAC,CAAC,OAAO,CAAC;IAC5C,MAAM,SAAS,GAAG,IAAA,oBAAY,GAAE,CAAC;IAEjC,IAAI,CAAC,SAAS,IAAI,CAAC,OAAO,EAAE,CAAC;QAC3B,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;IACxE,CAAC;IAED,MAAM,gBAAgB,GAAG,SAAS,CAAC,gBAAgB,CAAC;IAEpD,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,IAAA,0BAAiB,EAChD,QAAQ,EACR,SAAS,EACT,OAAO,EACP,gBAAgB,EAChB,gBAAgB,EAChB,SAAS,EACT,UAAU,CACX,CAAC;IAEF,MAAM,gBAAgB,GAAG,IAAA,6BAAoB,EAM3C,yBAAa,EAAE;QACf,QAAQ;QACR,GAAG,IAAI;QACP,UAAU;QACV,EAAE,EAAE,UAAU;QACd,gBAAgB;KACjB,CAAC,CAAC;IAEH,MAAM,EACJ,KAAK,EACL,WAAW,EACX,UAAU,EACV,QAAQ,EACR,iBAAiB,EAAE,mBAAmB,GACvC,GAAG,gBAAgB,CAAC;IAErB,MAAM,qBAAqB,GAAG,IAAA,eAAO,EACnC,GAAG,EAAE,CAAC,CAAC;QACL,GAAI,gBAAuE;QAC3E,UAAU;QACV,MAAM,EAAE,yBAAa;KACtB,CAAC,EACF,CAAC,gBAAgB,EAAE,UAAU,EAAE,yBAAa,CAAC,CAC9C,CAAC;IAEF,MAAM,iBAAiB,GAAG,IAAA,2BAAY,EAAC,CAAC,QAAyB,EAAE,EAAE,CAAC,CACpE,CAAC,iCAAoB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAC/C;MAAA,CAAC,4BAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,qBAAqB,CAAC,CACtD;QAAA,CAAC,mBAAmB,CAAC,CAAC,QAAQ,CAAC,EAAE,mBAAmB,CACtD;MAAA,EAAE,4BAAgB,CAAC,QAAQ,CAC7B;IAAA,EAAE,iCAAoB,CAAC,QAAQ,CAAC,CACjC,CAA0C,CAAC;IAE5C,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,iBAAiB,EAAE,QAAQ,EAAE,CAAC;AACzE,CAAC;AAED,SAAS,yBAAyB,CAChC,QAAmB,EACnB,iBAAkC,EAAE,EACpC,WAAW,GAAG,KAAK;IAEnB,gBAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;QACnC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAA,sBAAc,EAAC,KAAK,CAAC,IAAI,IAAA,mBAAS,EAAC,KAAK,CAAC,EAAE,CAAC;YACzD,OAAO;QACT,CAAC;QAED,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YACpE,OAAO,yBAAyB,CAC9B,KAAK,CAAC,KAAK,CAAC,QAAQ,EACpB,cAAc,EACd,WAAW,IAAI,IAAA,mBAAS,EAAC,KAAK,CAAC,CAChC,CAAC;QACJ,CAAC;QAED,IAAI,IAAA,mBAAS,EAAC,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,KAAK,UAAU,EAAE,CAAC;YACnE,IAAI,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC;YAEpC,iFAAiF;YACjF,IACE,KAAK,CAAC,KAAK,CAAC,OAAO;gBACnB,IAAA,sBAAc,EAAC,QAAQ,CAAC;gBACxB,QAAQ,CAAC,KAAK;gBACd,OAAO,QAAQ,CAAC,KAAK,KAAK,QAAQ;gBAClC,UAAU,IAAI,QAAQ,CAAC,KAAK,EAC5B,CAAC;gBACD,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAqB,CAAC;YAClD,CAAC;YAED,OAAO,yBAAyB,CAAC,QAAQ,EAAE,cAAc,EAAE,WAAW,IAAI,IAAA,mBAAS,EAAC,KAAK,CAAC,CAAC,CAAC;QAC9F,CAAC;QAED,8FAA8F;QAC9F,IAAI,CAAC,WAAW,IAAI,CAAC,IAAA,yBAAY,EAAC,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO;QACT,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC,KAAK,CAAC;QAEnC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;gBAC3C,OAAO,CAAC,IAAI,CACV,qBAAqB,IAAI,gGAAgG,CAC1H,CAAC;YACJ,CAAC;YACD,OAAO;QACT,CAAC;QAED,MAAM,YAAY,GAAG,IAAA,kBAAW,EAAC,IAAI,CAAC,CAAC;QAEvC,IAAI,IAAA,0BAAoB,EAAC,YAAY,CAAC,EAAE,CAAC;YACvC,OAAO,cAAc,CAAC,IAAI,CAAC;gBACzB,IAAI,EAAE,UAAU;gBAChB,IAAI;gBACJ,IAAI,EAAE,YAAY;aACnB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;gBAC3C,OAAO,CAAC,IAAI,CACV,yGAAyG,CAC1G,CAAC;YACJ,CAAC;YACD,OAAO;QACT,CAAC;QAED,OAAO,cAAc,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC;IAEH,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,UAAU,CACjB,KAAwB;IAExB,OAAO,KAAK,CAAC,IAAI,KAAK,gBAAQ,CAAC;AACjC,CAAC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,QAAQ,EAAE;QACR,IAAI,EAAE,CAAC;KACR;CACF,CAAC,CAAC", "sourcesContent": ["import {\n  DefaultNavigatorOptions,\n  LinkingContext,\n  ParamListBase,\n  TabActionHelpers,\n  TabNavigationState,\n  TabRouterOptions,\n  useNavigationBuilder,\n} from '@react-navigation/native';\nimport {\n  Children,\n  ComponentProps,\n  Fragment,\n  ReactElement,\n  ReactNode,\n  isValidElement,\n  use,\n  useMemo,\n  PropsWithChildren,\n} from 'react';\nimport { StyleSheet, ViewProps, View } from 'react-native';\n\nimport {\n  ExpoTabsScreenOptions,\n  TabNavigationEventMap,\n  TabTriggerMapContext,\n  TabsContextValue,\n} from './TabContext';\nimport { isTabList } from './TabList';\nimport { ExpoTabRouter, ExpoTabRouterOptions } from './TabRouter';\nimport { isTabSlot } from './TabSlot';\nimport { isTabTrigger } from './TabTrigger';\nimport { SafeAreaViewSlot, ScreenTrigger, triggersToScreens } from './common';\nimport { useComponent } from './useComponent';\nimport { useRouteNode, useContextKey } from '../Route';\nimport { useRouteInfo } from '../hooks';\nimport { resolveHref } from '../link/href';\nimport { shouldLinkExternally } from '../utils/url';\nimport { NavigatorContext, NavigatorContextValue } from '../views/Navigator';\n\nexport * from './TabContext';\nexport * from './TabList';\nexport * from './TabSlot';\nexport * from './TabTrigger';\nexport { ExpoTabsResetValue } from './TabRouter';\n\n/**\n * Options to provide to the Tab Router.\n */\nexport type UseTabsOptions = Omit<\n  DefaultNavigatorOptions<\n    ParamListBase,\n    any,\n    TabNavigationState<any>,\n    ExpoTabsScreenOptions,\n    TabNavigationEventMap,\n    any\n  >,\n  'children'\n> & {\n  backBehavior?: TabRouterOptions['backBehavior'];\n};\n\nexport type TabsProps = ViewProps & {\n  /** Forward props to child component and removes the extra `<View>`. Useful for custom wrappers. */\n  asChild?: boolean;\n  options?: UseTabsOptions;\n};\n\n/**\n * Root component for the headless tabs.\n *\n * @see [`useTabsWithChildren`](#usetabswithchildrenoptions) for a hook version of this component.\n * @example\n * ```tsx\n * <Tabs>\n *  <TabSlot />\n *  <TabList>\n *   <TabTrigger name=\"home\" href=\"/\" />\n *  </TabList>\n * </Tabs>\n * ```\n */\nexport function Tabs(props: TabsProps) {\n  const { children, asChild, options, ...rest } = props;\n  const Comp = asChild ? SafeAreaViewSlot : View;\n\n  const { NavigationContent } = useTabsWithChildren({\n    // asChild adds an extra layer, so we need to process the child's children\n    children:\n      asChild &&\n      isValidElement(children) &&\n      children.props &&\n      typeof children.props === 'object' &&\n      'children' in children.props\n        ? (children.props.children as ReactNode)\n        : children,\n    ...options,\n  });\n\n  return (\n    <Comp style={styles.tabsRoot} {...rest}>\n      <NavigationContent>{children}</NavigationContent>\n    </Comp>\n  );\n}\n\n// @docsMissing\nexport type UseTabsWithChildrenOptions = PropsWithChildren<UseTabsOptions>;\n\n// @docsMissing\nexport type UseTabsWithTriggersOptions = UseTabsOptions & {\n  triggers: ScreenTrigger[];\n};\n\n/**\n * Hook version of `Tabs`. The returned NavigationContent component\n * should be rendered. Using the hook requires using the `<TabList />`\n * and `<TabTrigger />` components exported from Expo Router.\n *\n * The `useTabsWithTriggers()` hook can be used for custom components.\n *\n * @see [`Tabs`](#tabs) for the component version of this hook.\n * @example\n * ```tsx\n * export function MyTabs({ children }) {\n *  const { NavigationContent } = useTabsWithChildren({ children })\n *\n *  return <NavigationContent />\n * }\n * ```\n */\nexport function useTabsWithChildren(options: UseTabsWithChildrenOptions) {\n  const { children, ...rest } = options;\n  return useTabsWithTriggers({ triggers: parseTriggersFromChildren(children), ...rest });\n}\n\n/**\n * Alternative hook version of `Tabs` that uses explicit triggers\n * instead of `children`.\n *\n * @see [`Tabs`](#tabs) for the component version of this hook.\n * @example\n * ```tsx\n * export function MyTabs({ children }) {\n *   const { NavigationContent } = useTabsWithChildren({ triggers: [] })\n *\n *   return <NavigationContent />\n * }\n * ```\n */\nexport function useTabsWithTriggers(options: UseTabsWithTriggersOptions): TabsContextValue {\n  const { triggers, ...rest } = options;\n  // Ensure we extend the parent triggers, so we can trigger them as well\n  const parentTriggerMap = use(TabTriggerMapContext);\n  const routeNode = useRouteNode();\n  const contextKey = useContextKey();\n  const linking = use(LinkingContext).options;\n  const routeInfo = useRouteInfo();\n\n  if (!routeNode || !linking) {\n    throw new Error('No RouteNode. This is likely a bug in expo-router.');\n  }\n\n  const initialRouteName = routeNode.initialRouteName;\n\n  const { children, triggerMap } = triggersToScreens(\n    triggers,\n    routeNode,\n    linking,\n    initialRouteName,\n    parentTriggerMap,\n    routeInfo,\n    contextKey\n  );\n\n  const navigatorContext = useNavigationBuilder<\n    TabNavigationState<any>,\n    ExpoTabRouterOptions,\n    TabActionHelpers<ParamListBase>,\n    ExpoTabsScreenOptions,\n    TabNavigationEventMap\n  >(ExpoTabRouter, {\n    children,\n    ...rest,\n    triggerMap,\n    id: contextKey,\n    initialRouteName,\n  });\n\n  const {\n    state,\n    descriptors,\n    navigation,\n    describe,\n    NavigationContent: RNNavigationContent,\n  } = navigatorContext;\n\n  const navigatorContextValue = useMemo<NavigatorContextValue>(\n    () => ({\n      ...(navigatorContext as unknown as ReturnType<typeof useNavigationBuilder>),\n      contextKey,\n      router: ExpoTabRouter,\n    }),\n    [navigatorContext, contextKey, ExpoTabRouter]\n  );\n\n  const NavigationContent = useComponent((children: React.ReactNode) => (\n    <TabTriggerMapContext.Provider value={triggerMap}>\n      <NavigatorContext.Provider value={navigatorContextValue}>\n        <RNNavigationContent>{children}</RNNavigationContent>\n      </NavigatorContext.Provider>\n    </TabTriggerMapContext.Provider>\n  )) as TabsContextValue['NavigationContent'];\n\n  return { state, descriptors, navigation, NavigationContent, describe };\n}\n\nfunction parseTriggersFromChildren(\n  children: ReactNode,\n  screenTriggers: ScreenTrigger[] = [],\n  isInTabList = false\n) {\n  Children.forEach(children, (child) => {\n    if (!child || !isValidElement(child) || isTabSlot(child)) {\n      return;\n    }\n\n    if (isFragment(child) && typeof child.props.children !== 'function') {\n      return parseTriggersFromChildren(\n        child.props.children,\n        screenTriggers,\n        isInTabList || isTabList(child)\n      );\n    }\n\n    if (isTabList(child) && typeof child.props.children !== 'function') {\n      let children = child.props.children;\n\n      // <TabList asChild /> adds an extra layer. We need to parse the child's children\n      if (\n        child.props.asChild &&\n        isValidElement(children) &&\n        children.props &&\n        typeof children.props === 'object' &&\n        'children' in children.props\n      ) {\n        children = children.props.children as ReactNode;\n      }\n\n      return parseTriggersFromChildren(children, screenTriggers, isInTabList || isTabList(child));\n    }\n\n    // We should only process TabTriggers within the TabList. All other components will be ignored\n    if (!isInTabList || !isTabTrigger(child)) {\n      return;\n    }\n\n    const { href, name } = child.props;\n\n    if (!href) {\n      if (process.env.NODE_ENV === 'development') {\n        console.warn(\n          `<TabTrigger name={${name}}> does not have a 'href' prop. TabTriggers within a <TabList /> are required to have an href.`\n        );\n      }\n      return;\n    }\n\n    const resolvedHref = resolveHref(href);\n\n    if (shouldLinkExternally(resolvedHref)) {\n      return screenTriggers.push({\n        type: 'external',\n        name,\n        href: resolvedHref,\n      });\n    }\n\n    if (!name) {\n      if (process.env.NODE_ENV === 'development') {\n        console.warn(\n          `<TabTrigger> does not have a 'name' prop. TabTriggers within a <TabList /> are required to have a name.`\n        );\n      }\n      return;\n    }\n\n    return screenTriggers.push({ type: 'internal', href: resolvedHref, name });\n  });\n\n  return screenTriggers;\n}\n\nfunction isFragment(\n  child: ReactElement<any>\n): child is ReactElement<ComponentProps<typeof Fragment>> {\n  return child.type === Fragment;\n}\n\nconst styles = StyleSheet.create({\n  tabsRoot: {\n    flex: 1,\n  },\n});\n"]}