{"version": 3, "file": "TabContext.js", "sourceRoot": "", "sources": ["../../src/ui/TabContext.tsx"], "names": [], "mappings": ";;;AAWA,iCAAsC;AA2EzB,QAAA,UAAU,GAAG,IAAA,qBAAa,EAAkB,EAAE,CAAC,CAAC;AAC7D;;GAEG;AACU,QAAA,oBAAoB,GAAG,IAAA,qBAAa,EAAa,EAAE,CAAC,CAAC;AAClE;;GAEG;AACU,QAAA,sBAAsB,GAAG,IAAA,qBAAa,EAAkC,EAAE,CAAC,CAAC;AACzF;;GAEG;AACU,QAAA,oBAAoB,GAAG,IAAA,qBAAa,EAAwC,IAAI,CAAC,CAAC;AAC/F;;GAEG;AACU,QAAA,gBAAgB,GAAG,IAAA,qBAAa,EAA4B;IACvE,IAAI,EAAE,KAAK;IACX,kBAAkB,EAAE,EAAE;IACtB,OAAO,EAAE,EAAE;IACX,KAAK,EAAE,CAAC,CAAC;IACT,GAAG,EAAE,EAAE;IACP,KAAK,EAAE,KAAK;IACZ,UAAU,EAAE,EAAE;IACd,MAAM,EAAE,EAAE;CACX,CAAC,CAAC", "sourcesContent": ["import { BottomTabNavigationOptions } from '@react-navigation/bottom-tabs';\nimport {\n  DefaultNavigatorOptions,\n  NavigationAction,\n  NavigationProp,\n  ParamListBase,\n  TabActionHelpers,\n  TabNavigationState,\n  TabRouterOptions,\n  useNavigationBuilder,\n} from '@react-navigation/native';\nimport { createContext } from 'react';\n\nimport { TriggerMap } from './common';\n\nexport type ExpoTabsProps = ExpoTabsNavigatorOptions;\n\nexport type ExpoTabsNavigatorScreenOptions = {\n  detachInactiveScreens?: boolean;\n  unmountOnBlur?: boolean;\n  freezeOnBlur?: boolean;\n  lazy?: boolean;\n};\n\nexport type ExpoTabsNavigatorOptions = DefaultNavigatorOptions<\n  ParamListBase,\n  string | undefined,\n  TabNavigationState<ParamListBase>,\n  ExpoTabsScreenOptions,\n  TabNavigationEventMap,\n  ExpoTabsNavigationProp<ParamListBase>\n> &\n  // Should be set through `unstable_settings`\n  Omit<TabRouterOptions, 'initialRouteName'> &\n  ExpoTabsNavigatorScreenOptions;\n\nexport type ExpoTabsNavigationProp<\n  ParamList extends ParamListBase,\n  RouteName extends keyof ParamList = keyof ParamList,\n  NavigatorID extends string | undefined = undefined,\n> = NavigationProp<\n  ParamList,\n  RouteName,\n  NavigatorID,\n  TabNavigationState<ParamListBase>,\n  ExpoTabsScreenOptions,\n  TabNavigationEventMap\n>;\n\nexport type ExpoTabsScreenOptions = Pick<\n  BottomTabNavigationOptions,\n  'title' | 'lazy' | 'freezeOnBlur'\n> & {\n  params?: object;\n  title: string;\n  action: NavigationAction;\n};\n\nexport type TabNavigationEventMap = {\n  /**\n   * Event which fires on tapping on the tab in the tab bar.\n   */\n  tabPress: { data: undefined; canPreventDefault: true };\n  /**\n   * Event which fires on long press on the tab in the tab bar.\n   */\n  tabLongPress: { data: undefined };\n};\n\n/**\n * The React Navigation custom navigator.\n *\n * @see [`useNavigationBuilder`](https://reactnavigation.org/docs/custom-navigators/#usenavigationbuilder) hook from React Navigation for more information.\n */\nexport type TabsContextValue = ReturnType<\n  typeof useNavigationBuilder<\n    TabNavigationState<any>,\n    TabRouterOptions,\n    TabActionHelpers<ParamListBase>,\n    ExpoTabsNavigatorScreenOptions,\n    TabNavigationEventMap\n  >\n>;\n\nexport type TabContextValue = TabsDescriptor['options'];\n\nexport const TabContext = createContext<TabContextValue>({});\n/**\n * @hidden\n */\nexport const TabTriggerMapContext = createContext<TriggerMap>({});\n/**\n * @hidden\n */\nexport const TabsDescriptorsContext = createContext<TabsContextValue['descriptors']>({});\n/**\n * @hidden\n */\nexport const TabsNavigatorContext = createContext<TabsContextValue['navigation'] | null>(null);\n/**\n * @hidden\n */\nexport const TabsStateContext = createContext<TabsContextValue['state']>({\n  type: 'tab',\n  preloadedRouteKeys: [],\n  history: [],\n  index: -1,\n  key: '',\n  stale: false,\n  routeNames: [],\n  routes: [],\n});\n\nexport type Route = TabNavigationState<ParamListBase>['routes'][number];\nexport type TabsDescriptor = TabsContextValue['descriptors'][number];\n"]}