{"version": 3, "file": "TabList.js", "sourceRoot": "", "sources": ["../../src/ui/TabList.tsx"], "names": [], "mappings": ";;AAuBA,0BAGC;AAKD,8BAIC;AAlCD,+CAA2D;AAE3D,qCAAoC;AAOpC;;;;;;;;;;;;GAYG;AACH,SAAgB,OAAO,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,KAAK,EAAgB;IAChE,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,iBAAQ,CAAC,CAAC,CAAC,mBAAI,CAAC;IACvC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,EAAG,CAAC;AAC7D,CAAC;AAED;;GAEG;AACH,SAAgB,SAAS,CACvB,KAAwB;IAExB,OAAO,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC;AAChC,CAAC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,OAAO,EAAE;QACP,aAAa,EAAE,KAAK;QACpB,cAAc,EAAE,eAAe;KAChC;IACD,UAAU,EAAE;QACV,aAAa,EAAE,KAAK;QACpB,cAAc,EAAE,eAAe;KAChC;CACF,CAAC,CAAC", "sourcesContent": ["import { ReactElement, ComponentProps } from 'react';\nimport { View, StyleSheet, ViewProps } from 'react-native';\n\nimport { ViewSlot } from './common';\n\nexport type TabListProps = ViewProps & {\n  /** Forward props to child component and removes the extra `<View>`. Useful for custom wrappers. */\n  asChild?: boolean;\n};\n\n/**\n * Wrapper component for `TabTriggers`. `TabTriggers` within the `TabList` define the tabs.\n *\n * @example\n * ```tsx\n * <Tabs>\n *  <TabSlot />\n *  <TabList>\n *   <TabTrigger name=\"home\" href=\"/\" />\n *  </TabList>\n * </Tabs>\n * ```\n */\nexport function TabList({ asChild, style, ...props }: TabListProps) {\n  const Comp = asChild ? ViewSlot : View;\n  return <Comp style={[styles.tabList, style]} {...props} />;\n}\n\n/**\n * @hidden\n */\nexport function isTabList(\n  child: ReactElement<any>\n): child is ReactElement<ComponentProps<typeof TabList>> {\n  return child.type === TabList;\n}\n\nconst styles = StyleSheet.create({\n  tabList: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n  },\n  tabTrigger: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n  },\n});\n"]}