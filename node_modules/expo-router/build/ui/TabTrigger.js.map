{"version": 3, "file": "TabTrigger.js", "sourceRoot": "", "sources": ["../../src/ui/TabTrigger.tsx"], "names": [], "mappings": ";;AAqEA,gCA4BC;AAKD,oCAIC;AAkCD,sCAwGC;AApPD,qDAA4C;AAE5C,iCAAkF;AAClF,+CAA2E;AAE3E,6CAAoD;AAGpD,+DAAyD;AACzD,sDAA2C;AAC3C,mEAAoE;AACpE,0CAAyD;AAEzD,kDAAyD;AAqCzD,MAAM,cAAc,GAAG,iBAA4D,CAAC;AAEpF;;;;;;;;;;;;;;;;GAgBG;AACH,SAAgB,UAAU,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,GAAG,SAAS,EAAE,GAAG,KAAK,EAAmB;IAC9F,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,aAAa,CAAC;QAC9C,IAAI;QACJ,KAAK;QACL,GAAG,KAAK;KACT,CAAC,CAAC;IAEH,sFAAsF;IACtF,IAAI,OAAO,EAAE,CAAC;QACZ,OAAO,CACL,CAAC,cAAc,CACb,KAAK,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CACzB,IAAI,KAAK,CAAC,CACV,IAAI,YAAY,CAAC,CACjB,IAAI,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,CAC5B;QAAA,CAAC,KAAK,CAAC,QAAQ,CACjB;MAAA,EAAE,cAAc,CAAC,CAClB,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,iEAAiE;QACjE,MAAM,mBAAmB,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;QAE5D,OAAO,CACL,CAAC,wBAAS,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,mBAAmB,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,YAAY,CAAC,CACxF;QAAA,CAAC,KAAK,CAAC,QAAQ,CACjB;MAAA,EAAE,wBAAS,CAAC,CACb,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,YAAY,CAC1B,KAAwB;IAExB,OAAO,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC;AACnC,CAAC;AA+BD;;GAEG;AACH,SAAgB,aAAa,CAAC,OAAwB;IACpD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,IAAA,+BAAmB,GAAE,CAAC;IACpD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;IACtD,MAAM,UAAU,GAAG,IAAA,WAAG,EAAC,iCAAoB,CAAC,CAAC;IAE7C,MAAM,UAAU,GAAG,IAAA,mBAAW,EAC5B,CAAC,IAAY,EAAE,EAAE;QACf,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO;QACT,CAAC;QAED,OAAO;YACL,SAAS,EAAE,KAAK,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK;YACvC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;YACjC,YAAY,EAAE,IAAA,qCAA0B,EAAC,IAAA,gCAAa,EAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACpE,GAAG,MAAM;SACV,CAAC;IACJ,CAAC,EACD,CAAC,UAAU,CAAC,CACb,CAAC;IAEF,MAAM,OAAO,GAAG,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAElE,MAAM,SAAS,GAAG,IAAA,mBAAW,EAC3B,CAAC,IAAY,EAAE,OAAyB,EAAE,EAAE;QAC1C,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;QAEhC,IAAI,MAAM,EAAE,CAAC;YACX,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC/B,OAAO,uBAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACN,OAAO,UAAU,EAAE,QAAQ,CAAC;oBAC1B,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE;wBACP,IAAI;wBACJ,GAAG,OAAO;qBACX;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,UAAU,EAAE,QAAQ,CAAC;gBAC1B,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE;oBACP,IAAI;iBACL;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC,EACD,CAAC,UAAU,EAAE,UAAU,CAAC,CACzB,CAAC;IAEF,MAAM,aAAa,GAAG,IAAA,mBAAW,EAC/B,CAAC,KAAK,EAAE,EAAE;QACR,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;QACjB,IAAI,CAAC,OAAO;YAAE,OAAO;QACrB,IAAI,KAAK,EAAE,kBAAkB,EAAE;YAAE,OAAO;QAExC,UAAU,EAAE,IAAI,CAAC;YACf,IAAI,EAAE,UAAU;YAChB,MAAM,EAAE,OAAO,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI;YACvE,iBAAiB,EAAE,IAAI;SACxB,CAAC,CAAC;QAEH,IAAI,CAAC,IAAA,2CAAsB,EAAC,KAAK,CAAC;YAAE,OAAO;QAE3C,SAAS,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE,KAAK,KAAK,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;IAC1E,CAAC,EACD,CAAC,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CAChC,CAAC;IAEF,MAAM,iBAAiB,GAAG,IAAA,mBAAW,EACnC,CAAC,KAAK,EAAE,EAAE;QACR,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;QACjB,IAAI,CAAC,OAAO;YAAE,OAAO;QACrB,IAAI,KAAK,EAAE,kBAAkB,EAAE;YAAE,OAAO;QAExC,UAAU,EAAE,IAAI,CAAC;YACf,IAAI,EAAE,cAAc;YACpB,MAAM,EAAE,OAAO,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI;SACxE,CAAC,CAAC;QAEH,IAAI,CAAC,IAAA,2CAAsB,EAAC,KAAK,CAAC;YAAE,OAAO;QAE3C,SAAS,CAAC,IAAI,EAAE;YACd,KAAK,EAAE,KAAK,KAAK,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK;SAClD,CAAC,CAAC;IACL,CAAC,EACD,CAAC,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,CACpC,CAAC;IAEF,MAAM,YAAY,GAAG;QACnB,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE,SAAS,CAAC;QACtC,OAAO,EAAE,aAAa;QACtB,WAAW,EAAE,iBAAiB;KAC/B,CAAC;IAEF,OAAO;QACL,SAAS;QACT,UAAU;QACV,OAAO;QACP,YAAY;KACb,CAAC;AACJ,CAAC;AAED,MAAM,MAAM,GAAG,yBAAU,CAAC,MAAM,CAAC;IAC/B,UAAU,EAAE;QACV,aAAa,EAAE,KAAK;QACpB,cAAc,EAAE,eAAe;KAChC;CACF,CAAC,CAAC", "sourcesContent": ["import { Slot } from '@radix-ui/react-slot';\nimport { TabNavigationState } from '@react-navigation/native';\nimport { ReactNode, use, ReactElement, ComponentProps, useCallback } from 'react';\nimport { View, StyleSheet, Pressable, PressableProps } from 'react-native';\n\nimport { TabTriggerMapContext } from './TabContext';\nimport { ExpoTabsResetValue } from './TabRouter';\nimport type { TriggerMap } from './common';\nimport { appendBaseUrl } from '../fork/getPathFromState';\nimport { router } from '../imperative-api';\nimport { shouldHandleMouseEvent } from '../link/useLinkToPathProps';\nimport { stripGroupSegmentsFromPath } from '../matchers';\nimport type { Href } from '../types';\nimport { useNavigatorContext } from '../views/Navigator';\n\ntype PressablePropsWithoutFunctionChildren = Omit<PressableProps, 'children'> & {\n  children?: ReactNode | undefined;\n};\n\nexport type TabTriggerProps = PressablePropsWithoutFunctionChildren & {\n  /**\n   *  Name of tab. When used within a `TabList` this sets the name of the tab.\n   * Otherwise, this references the name.\n   */\n  name: string;\n  /**\n   * Name of tab. Required when used within a `TabList`.\n   */\n  href?: Href;\n  /**\n   * Forward props to child component. Useful for custom wrappers.\n   */\n  asChild?: boolean;\n  /**\n   * Resets the route when switching to a tab.\n   */\n  reset?: SwitchToOptions['reset'] | 'onLongPress';\n};\n\nexport type TabTriggerOptions = {\n  name: string;\n  href: Href;\n};\n\nexport type TabTriggerSlotProps = PressablePropsWithoutFunctionChildren &\n  React.RefAttributes<View> & {\n    isFocused?: boolean;\n    href?: string;\n  };\n\nconst TabTriggerSlot = Slot as React.ForwardRefExoticComponent<TabTriggerSlotProps>;\n\n/**\n * Creates a trigger to navigate to a tab. When used as child of `TabList`, its\n * functionality slightly changes since the `href` prop is required,\n * and the trigger also defines what routes are present in the `Tabs`.\n *\n * When used outside of `TabList`, this component no longer requires an `href`.\n *\n * @example\n * ```tsx\n * <Tabs>\n *  <TabSlot />\n *  <TabList>\n *   <TabTrigger name=\"home\" href=\"/\" />\n *  </TabList>\n * </Tabs>\n * ```\n */\nexport function TabTrigger({ asChild, name, href, reset = 'onFocus', ...props }: TabTriggerProps) {\n  const { trigger, triggerProps } = useTabTrigger({\n    name,\n    reset,\n    ...props,\n  });\n\n  // Pressable doesn't accept the extra props, so only pass them if we are using asChild\n  if (asChild) {\n    return (\n      <TabTriggerSlot\n        style={styles.tabTrigger}\n        {...props}\n        {...triggerProps}\n        href={trigger?.resolvedHref}>\n        {props.children}\n      </TabTriggerSlot>\n    );\n  } else {\n    // These props are not typed, but are allowed by React Native Web\n    const reactNativeWebProps = { href: trigger?.resolvedHref };\n\n    return (\n      <Pressable style={styles.tabTrigger} {...reactNativeWebProps} {...props} {...triggerProps}>\n        {props.children}\n      </Pressable>\n    );\n  }\n}\n\n/**\n * @hidden\n */\nexport function isTabTrigger(\n  child: ReactElement<any>\n): child is ReactElement<ComponentProps<typeof TabTrigger>> {\n  return child.type === TabTrigger;\n}\n\n/**\n * Options for `switchTab` function.\n */\nexport type SwitchToOptions = {\n  /**\n   * Navigate and reset the history.\n   */\n  reset?: ExpoTabsResetValue;\n};\n\nexport type Trigger = TriggerMap[string] & {\n  isFocused: boolean;\n  resolvedHref: string;\n  route: TabNavigationState<any>['routes'][number];\n};\n\nexport type UseTabTriggerResult = {\n  switchTab: (name: string, options: SwitchToOptions) => void;\n  getTrigger: (name: string) => Trigger | undefined;\n  trigger?: Trigger;\n  triggerProps: TriggerProps;\n};\n\nexport type TriggerProps = {\n  isFocused: boolean;\n  onPress: PressableProps['onPress'];\n  onLongPress: PressableProps['onLongPress'];\n};\n\n/**\n * Utility hook creating custom `TabTrigger`.\n */\nexport function useTabTrigger(options: TabTriggerProps): UseTabTriggerResult {\n  const { state, navigation } = useNavigatorContext();\n  const { name, reset, onPress, onLongPress } = options;\n  const triggerMap = use(TabTriggerMapContext);\n\n  const getTrigger = useCallback(\n    (name: string) => {\n      const config = triggerMap[name];\n\n      if (!config) {\n        return;\n      }\n\n      return {\n        isFocused: state.index === config.index,\n        route: state.routes[config.index],\n        resolvedHref: stripGroupSegmentsFromPath(appendBaseUrl(config.href)),\n        ...config,\n      };\n    },\n    [triggerMap]\n  );\n\n  const trigger = name !== undefined ? getTrigger(name) : undefined;\n\n  const switchTab = useCallback(\n    (name: string, options?: SwitchToOptions) => {\n      const config = triggerMap[name];\n\n      if (config) {\n        if (config.type === 'external') {\n          return router.navigate(config.href);\n        } else {\n          return navigation?.dispatch({\n            type: 'JUMP_TO',\n            payload: {\n              name,\n              ...options,\n            },\n          });\n        }\n      } else {\n        return navigation?.dispatch({\n          type: 'JUMP_TO',\n          payload: {\n            name,\n          },\n        });\n      }\n    },\n    [navigation, triggerMap]\n  );\n\n  const handleOnPress = useCallback<NonNullable<PressableProps['onPress']>>(\n    (event) => {\n      onPress?.(event);\n      if (!trigger) return;\n      if (event?.isDefaultPrevented()) return;\n\n      navigation?.emit({\n        type: 'tabPress',\n        target: trigger.type === 'internal' ? trigger.route.key : trigger?.href,\n        canPreventDefault: true,\n      });\n\n      if (!shouldHandleMouseEvent(event)) return;\n\n      switchTab(name, { reset: reset !== 'onLongPress' ? reset : undefined });\n    },\n    [onPress, name, reset, trigger]\n  );\n\n  const handleOnLongPress = useCallback<NonNullable<PressableProps['onPress']>>(\n    (event) => {\n      onPress?.(event);\n      if (!trigger) return;\n      if (event?.isDefaultPrevented()) return;\n\n      navigation?.emit({\n        type: 'tabLongPress',\n        target: trigger.type === 'internal' ? trigger.route.key : trigger?.href,\n      });\n\n      if (!shouldHandleMouseEvent(event)) return;\n\n      switchTab(name, {\n        reset: reset === 'onLongPress' ? 'always' : reset,\n      });\n    },\n    [onLongPress, name, reset, trigger]\n  );\n\n  const triggerProps = {\n    isFocused: Boolean(trigger?.isFocused),\n    onPress: handleOnPress,\n    onLongPress: handleOnLongPress,\n  };\n\n  return {\n    switchTab,\n    getTrigger,\n    trigger,\n    triggerProps,\n  };\n}\n\nconst styles = StyleSheet.create({\n  tabTrigger: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n  },\n});\n"]}