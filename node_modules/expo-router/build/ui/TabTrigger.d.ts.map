{"version": 3, "file": "TabTrigger.d.ts", "sourceRoot": "", "sources": ["../../src/ui/TabTrigger.tsx"], "names": [], "mappings": "AACA,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAC;AAC9D,OAAO,EAAE,SAAS,EAAO,YAAY,EAAE,cAAc,EAAe,MAAM,OAAO,CAAC;AAClF,OAAO,EAAE,IAAI,EAAyB,cAAc,EAAE,MAAM,cAAc,CAAC;AAG3E,OAAO,EAAE,kBAAkB,EAAE,MAAM,aAAa,CAAC;AACjD,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AAK3C,OAAO,KAAK,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAGrC,KAAK,qCAAqC,GAAG,IAAI,CAAC,cAAc,EAAE,UAAU,CAAC,GAAG;IAC9E,QAAQ,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC;CAClC,CAAC;AAEF,MAAM,MAAM,eAAe,GAAG,qCAAqC,GAAG;IACpE;;;OAGG;IACH,IAAI,EAAE,MAAM,CAAC;IACb;;OAEG;IACH,IAAI,CAAC,EAAE,IAAI,CAAC;IACZ;;OAEG;IACH,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB;;OAEG;IACH,KAAK,CAAC,EAAE,eAAe,CAAC,OAAO,CAAC,GAAG,aAAa,CAAC;CAClD,CAAC;AAEF,MAAM,MAAM,iBAAiB,GAAG;IAC9B,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,IAAI,CAAC;CACZ,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG,qCAAqC,GACrE,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG;IAC1B,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,IAAI,CAAC,EAAE,MAAM,CAAC;CACf,CAAC;AAIJ;;;;;;;;;;;;;;;;GAgBG;AACH,wBAAgB,UAAU,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAiB,EAAE,GAAG,KAAK,EAAE,EAAE,eAAe,+BA4B/F;AAED;;GAEG;AACH,wBAAgB,YAAY,CAC1B,KAAK,EAAE,YAAY,CAAC,GAAG,CAAC,GACvB,KAAK,IAAI,YAAY,CAAC,cAAc,CAAC,OAAO,UAAU,CAAC,CAAC,CAE1D;AAED;;GAEG;AACH,MAAM,MAAM,eAAe,GAAG;IAC5B;;OAEG;IACH,KAAK,CAAC,EAAE,kBAAkB,CAAC;CAC5B,CAAC;AAEF,MAAM,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC,GAAG;IACzC,SAAS,EAAE,OAAO,CAAC;IACnB,YAAY,EAAE,MAAM,CAAC;IACrB,KAAK,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC;CAClD,CAAC;AAEF,MAAM,MAAM,mBAAmB,GAAG;IAChC,SAAS,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,KAAK,IAAI,CAAC;IAC5D,UAAU,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,OAAO,GAAG,SAAS,CAAC;IAClD,OAAO,CAAC,EAAE,OAAO,CAAC;IAClB,YAAY,EAAE,YAAY,CAAC;CAC5B,CAAC;AAEF,MAAM,MAAM,YAAY,GAAG;IACzB,SAAS,EAAE,OAAO,CAAC;IACnB,OAAO,EAAE,cAAc,CAAC,SAAS,CAAC,CAAC;IACnC,WAAW,EAAE,cAAc,CAAC,aAAa,CAAC,CAAC;CAC5C,CAAC;AAEF;;GAEG;AACH,wBAAgB,aAAa,CAAC,OAAO,EAAE,eAAe,GAAG,mBAAmB,CAwG3E"}