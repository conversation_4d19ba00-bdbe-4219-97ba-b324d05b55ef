{"version": 3, "file": "exports.js", "sourceRoot": "", "sources": ["../src/exports.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kBAAkB;AAClB,iDAAoD;AAmB3C,0FAnBA,qBAAS,OAmBA;AAAE,qFAnBA,gBAAI,OAmBA;AAjBxB,iCAUiB;AATf,kGAAA,SAAS,OAAA;AACT,8GAAA,qBAAqB,OAAA;AACrB,oGAAA,WAAW,OAAA;AACX,kHAAA,yBAAyB,OAAA;AACzB,8GAAA,qBAAqB,OAAA;AACrB,6GAAA,oBAAoB,OAAA;AACpB,oGAAA,WAAW,OAAA;AACX,0GAAA,iBAAiB,OAAA;AACjB,+GAAA,sBAAsB,OAAA;AAGxB,mDAAkD;AAAzC,wGAAA,MAAM,OAAA;AAEf,oCAAuF;AAA9E,4FAAA,IAAI,OAAA;AAAE,gGAAA,QAAQ,OAAA;AAEvB,iEAAgE;AAAvD,sHAAA,iBAAiB,OAAA;AAG1B,oBAAoB;AACpB,uCAAsC;AAA7B,oGAAA,QAAQ,OAAA;AACjB,+CAA8C;AAArC,sGAAA,SAAS,OAAA;AAClB,2CAA0C;AAAjC,kGAAA,OAAO,OAAA;AAChB,iDAA6D;AAApD,wGAAA,UAAU,OAAA;AAEnB,uDAAsD;AAA7C,8GAAA,aAAa,OAAA;AAGtB,WAAW;AACX;;GAEG;AACH,+DAA+C;AAE/C,mBAAmB;AACnB,iDAAgD;AAAvC,8GAAA,aAAa,OAAA;AACtB,mDAAkE;AAAzD,gHAAA,cAAc,OAAA", "sourcesContent": ["// Expo Router API\nimport { Navigator, Slot } from './views/Navigator';\n\nexport {\n  useRouter,\n  useUnstableGlobalHref,\n  usePathname,\n  useNavigationContainerRef,\n  useGlobalSearchParams,\n  useLocalSearchParams,\n  useSegments,\n  useRootNavigation,\n  useRootNavigationState,\n} from './hooks';\n\nexport { router, Router } from './imperative-api';\n\nexport { Link, Redirect, RedirectProps, LinkProps, WebAnchorProps } from './link/Link';\n\nexport { withLayoutContext } from './layouts/withLayoutContext';\nexport { Navigator, Slot };\n\n// Expo Router Views\nexport { ExpoRoot } from './ExpoRoot';\nexport { Unmatched } from './views/Unmatched';\nexport { Sitemap } from './views/Sitemap';\nexport { useSitemap, SitemapType } from './views/useSitemap';\nexport { ErrorBoundaryProps } from './views/Try';\nexport { ErrorBoundary } from './views/ErrorBoundary';\nexport type { ScreenProps } from './useScreens';\n\n// Platform\n/**\n * @hidden\n */\nexport * as SplashScreen from './views/Splash';\n\n// React Navigation\nexport { useNavigation } from './useNavigation';\nexport { useFocusEffect, EffectCallback } from './useFocusEffect';\nexport type { ResultState } from './fork/getStateFromPath';\n\nexport type { RedirectConfig } from './getRoutesCore';\nexport type { SingularOptions } from './useScreens';\n\nexport type * from './types';\n"]}