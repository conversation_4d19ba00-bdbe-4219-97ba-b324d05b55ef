{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/doctor/index.ts"], "names": [], "mappings": ";;;;;AAwBA,wBAqEC;AA7FD,kDAAgC;AAEhC,kEAAqD;AAErD,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,oBAAoB,CAAC,CAAC;AAEhD;;;;GAIG;AACH,MAAM,SAAS,GAAsB,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAEnE,MAAM,kBAAkB,GAAG,MAAM,CAAC,OAAO,CACvC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,SAAS,CAAC,YAAY,EAAE,SAAS,CAAC,gBAAgB,CAAC,CACtE,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,mBAAmB,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;AASlF,SAAgB,MAAM,CACpB,GAAsB,EACtB,sBAA8B;AAC9B,+CAA+C;AAC/C,EACE,IAAI,EACJ,SAAS,GAIV;IAED,MAAM,oBAAoB,GAAG,EAAE,GAAG,GAAG,CAAC,YAAY,EAAE,GAAG,GAAG,CAAC,eAAe,EAAE,CAAC;IAC7E,MAAM,sBAAsB,GAAG,OAAO,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;IAE3E,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACzD,MAAM,qBAAqB,GAA0B,EAAE,CAAC;IAExD;;;;;;;;OAQG;IACH,IACE,YAAY,CAAC,GAAG,CAAC,0BAA0B,CAAC;QAC5C,sBAAsB,KAAK,sBAAsB,EACjD,CAAC;QACD,OAAO,CAAC,IAAI,CACV,iCAAiC,IAAI,CAAC,0BAA0B,CAAC,YAAY,IAAI,CAAC,cAAc,CAAC,iEAAiE,SAAS,CAAC,yCAAyC,CAAC,GAAG,CAC1N,CAAC;IACJ,CAAC;IAED,KAAK,MAAM,CAAC,GAAG,EAAE,YAAY,CAAC,IAAI,kBAAkB,EAAE,CAAC;QACrD,IAAI,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAC1B,KAAK,CAAC,YAAY,GAAG,uCAAuC,CAAC,CAAC;YAC9D,SAAS;QACX,CAAC;QAED,MAAM,UAAU,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC;QAE7C;;;;WAIG;QACH,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,SAAS;QACX,CAAC;QAED,KAAK,CAAC,YAAY,GAAG,SAAS,YAAY,cAAc,UAAU,EAAE,CAAC,CAAC;QACtE,IAAI,CAAC,UAAU,IAAI,IAAA,gBAAiB,EAAC,YAAY,EAAE,UAAU,CAAC,EAAE,CAAC;YAC/D,SAAS;QACX,CAAC;QAED,KAAK,CAAC,kCAAkC,GAAG,EAAE,CAAC,CAAC;QAE/C,qBAAqB,CAAC,IAAI,CAAC;YACzB,WAAW,EAAE,GAAG;YAChB,WAAW,EAAE,GAAG,CAAC,YAAY,IAAI,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,iBAAiB;YAC7F,sBAAsB,EAAE,YAAY;YACpC,aAAa,EAAE,UAAU;SAC1B,CAAC,CAAC;IACL,CAAC;IAED,OAAO,qBAAqB,CAAC;AAC/B,CAAC", "sourcesContent": ["import createDebug from 'debug';\nimport type { PackageJSONConfig } from 'expo/config';\nimport semverRangeSubset from 'semver/ranges/subset';\n\nconst debug = createDebug('expo:router:doctor');\n\n/**\n * Small hack to get the package.json.\n * We do no use import() as this would require changing the rootDir in `tsconfig.json`,\n * which in turn will change the structure of the outDir.\n */\nconst routerPkg: PackageJSONConfig = require('../../package.json');\n\nconst routerDependencies = Object.entries<string>(\n  Object.assign({}, routerPkg.dependencies, routerPkg.peerDependencies)\n).filter((entry) => entry[0].startsWith('@react-navigation') && entry[1] !== '*');\n\ntype IncorrectDependency = {\n  packageName: string;\n  packageType: 'dependencies' | 'devDependencies';\n  expectedVersionOrRange: string;\n  actualVersion: string;\n};\n\nexport function doctor(\n  pkg: PackageJSONConfig,\n  appReactNavigationPath: string,\n  // Reuse the formatting functions from expo-cli\n  {\n    bold,\n    learnMore,\n  }: {\n    bold: (text: string) => string;\n    learnMore: (url: string, options?: { learnMoreMessage?: string; dim?: boolean }) => string;\n  }\n): IncorrectDependency[] {\n  const resolvedDependencies = { ...pkg.dependencies, ...pkg.devDependencies };\n  const libReactNavigationPath = require.resolve('@react-navigation/native');\n\n  const userExcluded = new Set(pkg.expo?.install?.exclude);\n  const incorrectDependencies: IncorrectDependency[] = [];\n\n  /**\n   * If the user has a dependency with a sub-dependency on @react-navigation/native, this may install a different\n   * version of @react-navigation/native than the one required by expo-router.\n   *\n   * To detect this, we require the caller of this function to first resolve their path to @react-navigation/native, as\n   * they will get the 'top' level package. When expo-router resolves the path to @react-navigation/native, if it is different\n   * when the versions must not have matched and the package manager installed a nested node_module folder with a different\n   * version of @react-navigation/native.\n   */\n  if (\n    userExcluded.has('@react-navigation/native') &&\n    appReactNavigationPath !== libReactNavigationPath\n  ) {\n    console.warn(\n      `Detected multiple versions of ${bold('@react-navigation/native')} in your ${bold('node_modules')}. This may lead to unexpected navigation behavior and errors. ${learnMore('https://expo.fyi/router-navigation-deps')}.`\n    );\n  }\n\n  for (const [dep, allowedRange] of routerDependencies) {\n    if (userExcluded.has(dep)) {\n      debug(`Skipping ${dep} because it is excluded in the config`);\n      continue;\n    }\n\n    const usersRange = resolvedDependencies[dep];\n\n    /**\n     * routerDependencies contains all the dependencies that are required by expo-router,\n     * both peerDependencies and dependencies. If the user has not manually installed\n     * them, then we should skip them.\n     */\n    if (!usersRange) {\n      continue;\n    }\n\n    debug(`Checking ${dep} with ${allowedRange} and found ${usersRange}`);\n    if (!usersRange || semverRangeSubset(allowedRange, usersRange)) {\n      continue;\n    }\n\n    debug(`Incorrect dependency found for ${dep}`);\n\n    incorrectDependencies.push({\n      packageName: dep,\n      packageType: pkg.dependencies && dep in pkg.dependencies ? 'dependencies' : 'devDependencies',\n      expectedVersionOrRange: allowedRange,\n      actualVersion: usersRange,\n    });\n  }\n\n  return incorrectDependencies;\n}\n"]}