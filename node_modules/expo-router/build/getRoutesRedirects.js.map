{"version": 3, "file": "getRoutesRedirects.js", "sourceRoot": "", "sources": ["../src/getRoutesRedirects.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,wCA4BC;AAED,8CA6BC;AAED,0CAoBC;AAED,wDAeC;AA3GD,sDAAwC;AACxC,iCAAiD;AAEjD,0EAA0D;AAG1D,yCAA8C;AAC9C,qCAAmD;AAEnD,SAAgB,cAAc,CAC5B,GAA8B,EAC9B,SAAuC;IAEvC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC;QAC1C,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,OAAO,GAAG,IAAA,kCAAS,EAAC,GAAG,CAAC,CAAC;IAC/B,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAElE,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,OAAO,GAAG,CAAC;IACb,CAAC;IAED,4CAA4C;IAC5C,IAAI,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;QAChB,IAAI,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;QAEnC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YAC3D,IAAI,GAAG,SAAS,IAAI,EAAE,CAAC;QACzB,CAAC;QAED,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,cAAc,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;AACtE,CAAC;AAED,SAAgB,iBAAiB,CAAC,cAA8B;IAC9D,OAAO;QACL,OAAO,EAAE,SAAS,iBAAiB;YACjC,MAAM,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;YAElD,MAAM,UAAU,GAAG,IAAA,0BAAoB,EAAC,cAAc,CAAC,WAAW,CAAC,CAAC;YAEpE,IAAA,iBAAS,EAAC,GAAG,EAAE;gBACb,IAAI,UAAU,EAAE,CAAC;oBACf,IAAI,IAAI,GAAG,cAAc,CAAC,WAAW,CAAC;oBACtC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;wBAC3D,IAAI,GAAG,SAAS,IAAI,EAAE,CAAC;oBACzB,CAAC;oBAED,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC,EAAE,EAAE,CAAC,CAAC;YAEP,IAAI,UAAU,EAAE,CAAC;gBACf,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,IAAI,GAAG,eAAe,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;YAEvD,OAAO,IAAA,qBAAa,EAAC,OAAO,CAAC,aAAa,CAAC,CAAC,QAAQ,EAAE;gBACpD,IAAI;aACL,CAAC,CAAC;QACL,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAgB,eAAe,CAAC,IAAY,EAAE,MAAsB;IAClE,MAAM,MAAM,GAAsC,EAAE,CAAC;IAErD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAE7C,KAAK,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,WAAW,CAAC,OAAO,EAAE,EAAE,CAAC;QACxD,MAAM,WAAW,GAAG,IAAA,2BAAgB,EAAC,UAAU,CAAC,CAAC;QACjD,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,SAAS;QACX,CAAC;aAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;YAC7B,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;YACxC,SAAS;QACX,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM;QACR,CAAC;IACH,CAAC;IAED,OAAO,sBAAsB,CAAC,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AAC5D,CAAC;AAED,SAAgB,sBAAsB,CAAC,IAAY,EAAE,MAAyC;IAC5F,OAAO,IAAI;SACR,KAAK,CAAC,GAAG,CAAC;SACV,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QACZ,MAAM,WAAW,GAAG,IAAA,2BAAgB,EAAC,IAAI,CAAC,CAAC;QAC3C,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACvC,OAAO,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAChC,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,CAAC;SACD,MAAM,CAAC,OAAO,CAAC;SACf,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,CAAC", "sourcesContent": ["import * as Linking from 'expo-linking';\nimport { createElement, useEffect } from 'react';\n\nimport { cleanPath } from './fork/getStateFromPath-forks';\nimport { RedirectConfig } from './getRoutesCore';\nimport type { StoreRedirects } from './global-state/router-store';\nimport { matchDynamicName } from './matchers';\nimport { shouldLinkExternally } from './utils/url';\n\nexport function applyRedirects(\n  url: string | null | undefined,\n  redirects: StoreRedirects[] | undefined\n): string | undefined | null {\n  if (typeof url !== 'string' || !redirects) {\n    return url;\n  }\n\n  const nextUrl = cleanPath(url);\n  const redirect = redirects.find(([regex]) => regex.test(nextUrl));\n\n  if (!redirect) {\n    return url;\n  }\n\n  // If the redirect is external, open the URL\n  if (redirect[2]) {\n    let href = redirect[1].destination;\n\n    if (href.startsWith('//') && process.env.EXPO_OS !== 'web') {\n      href = `https:${href}`;\n    }\n\n    Linking.openURL(href);\n    return href;\n  }\n\n  return applyRedirects(convertRedirect(url, redirect[1]), redirects);\n}\n\nexport function getRedirectModule(redirectConfig: RedirectConfig) {\n  return {\n    default: function RedirectComponent() {\n      const pathname = require('./hooks').usePathname();\n\n      const isExternal = shouldLinkExternally(redirectConfig.destination);\n\n      useEffect(() => {\n        if (isExternal) {\n          let href = redirectConfig.destination;\n          if (href.startsWith('//') && process.env.EXPO_OS !== 'web') {\n            href = `https:${href}`;\n          }\n\n          Linking.openURL(href);\n        }\n      }, []);\n\n      if (isExternal) {\n        return null;\n      }\n\n      const href = convertRedirect(pathname, redirectConfig);\n\n      return createElement(require('./link/Link').Redirect, {\n        href,\n      });\n    },\n  };\n}\n\nexport function convertRedirect(path: string, config: RedirectConfig) {\n  const params: Record<string, string | string[]> = {};\n\n  const parts = path.split('/');\n  const sourceParts = config.source.split('/');\n\n  for (const [index, sourcePart] of sourceParts.entries()) {\n    const dynamicName = matchDynamicName(sourcePart);\n    if (!dynamicName) {\n      continue;\n    } else if (!dynamicName.deep) {\n      params[dynamicName.name] = parts[index];\n      continue;\n    } else {\n      params[dynamicName.name] = parts.slice(index);\n      break;\n    }\n  }\n\n  return mergeVariablesWithPath(config.destination, params);\n}\n\nexport function mergeVariablesWithPath(path: string, params: Record<string, string | string[]>) {\n  return path\n    .split('/')\n    .map((part) => {\n      const dynamicName = matchDynamicName(part);\n      if (!dynamicName) {\n        return part;\n      } else {\n        const param = params[dynamicName.name];\n        delete params[dynamicName.name];\n        return param;\n      }\n    })\n    .filter(Boolean)\n    .join('/');\n}\n"]}