{"version": 3, "file": "getRoutesCore.js", "sourceRoot": "", "sources": ["../src/getRoutesCore.ts"], "names": [], "mappings": ";;AAgFA,8BAeC;AAmjBD,8CAwBC;AAED,0CAgBC;AA3rBD,yCASoB;AAEpB,qCAAmD;AAsDnD,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC;AAEpE;;;;;;;;;;;GAWG;AACH,SAAgB,SAAS,CAAC,aAA6B,EAAE,OAAgB;IACvE,MAAM,aAAa,GAAG,gBAAgB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IAE/D,yBAAyB;IACzB,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,QAAQ,GAAG,4BAA4B,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;IAEtE,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAC/B,wCAAwC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC9D,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,aAA6B,EAAE,OAAgB;IACvE,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC;IAE7E,MAAM,UAAU,GAAa,CAAC,uCAAuC,CAAC,CAAC,CAAC,oCAAoC;IAE5G,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACnB,UAAU,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IACD,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;QAC/B,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;IAC/C,CAAC;IAED,MAAM,aAAa,GAAkB;QACnC,KAAK,EAAE,IAAI,GAAG,EAAE;QAChB,cAAc,EAAE,IAAI,GAAG,EAAE;KAC1B,CAAC;IAEF,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,IAAI,OAAO,GAAG,KAAK,CAAC;IAEpB,MAAM,WAAW,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC;IACzC,MAAM,SAAS,GAAmC,EAAE,CAAC;IACrD,MAAM,QAAQ,GAAkC,EAAE,CAAC;IAEnD,IAAI,yBAA6F,CAAC;IAElG,MAAM,oBAAoB,GAAG,GAAG,EAAE;QAChC,2DAA2D;QAC3D,yBAAyB,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YACpD,OAAO;gBACL,UAAU,EAAE,GAAG;gBACf,oBAAoB,EAAE,+CAA+C,CACnE,IAAA,oCAAyB,EAAC,GAAG,CAAC,CAC/B;aACF,CAAC;QACJ,CAAC,CAAC,CAAC;QACH,OAAO,yBAAyB,CAAC;IACnC,CAAC,CAAC;IAEF,2FAA2F;IAC3F,4GAA4G;IAC5G,IAAI,OAAO,CAAC,2BAA2B,EAAE,CAAC;QACxC,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;YACtB,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACzC,MAAM,gBAAgB,GAAG,qCAAqC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAChF,MAAM,UAAU,GAAG,uBAAuB,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAE5D,MAAM,kBAAkB,GAAG,IAAA,0BAAoB,EAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAEtE,MAAM,qBAAqB,GAAG,kBAAkB;oBAC9C,CAAC,CAAC,QAAQ,CAAC,WAAW;oBACtB,CAAC,CAAC,+CAA+C,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAE1E,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC;oBAC7D,SAAS;gBACX,CAAC;gBAED,MAAM,gBAAgB,GAAG,kBAAkB;oBACzC,CAAC,CAAC,SAAS;oBACX,CAAC,CAAC,oBAAoB,EAAE,CAAC,IAAI,CACzB,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,oBAAoB,KAAK,qBAAqB,CAC5D,CAAC;gBACN,MAAM,WAAW,GAAG,kBAAkB;oBACpC,CAAC,CAAC,qBAAqB;oBACvB,CAAC,CAAC,gBAAgB,EAAE,oBAAoB,CAAC;gBAC3C,MAAM,qBAAqB,GAAG,kBAAkB;oBAC9C,CAAC,CAAC,qBAAqB;oBACvB,CAAC,CAAC,gBAAgB,EAAE,UAAU,CAAC;gBAEjC,IAAI,CAAC,qBAAqB,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;oBACxD;;;;;uBAKG;oBACH,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;wBAC9B,MAAM,IAAI,KAAK,CAAC,yBAAyB,QAAQ,CAAC,WAAW,mBAAmB,CAAC,CAAC;oBACpF,CAAC;oBAED,SAAS;gBACX,CAAC;gBAED,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACnC,SAAS,CAAC,UAAU,CAAC,GAAG;oBACtB,MAAM,EAAE,UAAU;oBAClB,WAAW;oBACX,qBAAqB;oBACrB,SAAS,EAAE,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC;oBACtC,QAAQ,EAAE,kBAAkB;oBAC5B,OAAO,EAAE,QAAQ,CAAC,OAAO;iBAC1B,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACrB,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACvC,MAAM,gBAAgB,GAAG,qCAAqC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAC/E,MAAM,UAAU,GAAG,uBAAuB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBAE3D,MAAM,qBAAqB,GAAG,uBAAuB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;gBAE3E,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC;oBAC7D,SAAS;gBACX,CAAC;gBAED,MAAM,gBAAgB,GAAG,oBAAoB,EAAE,CAAC,IAAI,CAClD,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,oBAAoB,KAAK,qBAAqB,CAC5D,CAAC;gBACF,MAAM,WAAW,GAAG,gBAAgB,EAAE,oBAAoB,CAAC;gBAC3D,MAAM,qBAAqB,GAAG,gBAAgB,EAAE,UAAU,CAAC;gBAE3D,IAAI,CAAC,qBAAqB,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;oBACxD;;;;;uBAKG;oBACH,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC;wBAC9B,MAAM,IAAI,KAAK,CAAC,wBAAwB,OAAO,CAAC,WAAW,mBAAmB,CAAC,CAAC;oBAClF,CAAC;oBAED,SAAS;gBACX,CAAC;gBAED,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBACnC,QAAQ,CAAC,UAAU,CAAC,GAAG;oBACrB,MAAM,EAAE,UAAU;oBAClB,WAAW;oBACX,qBAAqB;oBACrB,OAAO,EAAE,OAAO,CAAC,OAAO;iBACzB,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,0BAA0B,GAAG,IAAI,GAAG,EAAU,CAAC;IAErD,KAAK,MAAM,QAAQ,IAAI,WAAW,EAAE,CAAC;QACnC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;YACrD,SAAS;QACX,CAAC;QAED,OAAO,GAAG,IAAI,CAAC;QAEf,MAAM,IAAI,GAAG,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QAEjE,+EAA+E;QAC/E,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,CAAC;YACzB,SAAS;QACX,CAAC;QAED,IAAI,IAAI,GAAc;YACpB,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO;YAC7D,SAAS;gBACP,IAAI,WAAgB,CAAC;gBAErB,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;oBAChC,IAAI,CAAC;wBACH,WAAW,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC;oBACxC,CAAC;oBAAC,MAAM,CAAC;wBACP,WAAW,GAAG,EAAE,CAAC;oBACnB,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,WAAW,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC;gBACxC,CAAC;gBAED,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;oBACpE,0HAA0H;oBAC1H,yGAAyG;oBACzG,IAAI,WAAW,YAAY,OAAO,EAAE,CAAC;wBACnC,MAAM,IAAI,KAAK,CACb,UAAU,QAAQ,sDAAsD,CACzE,CAAC;oBACJ,CAAC;oBAED,MAAM,aAAa,GAAG,WAAW,EAAE,OAAO,CAAC;oBAC3C,IAAI,aAAa,YAAY,OAAO,EAAE,CAAC;wBACrC,MAAM,IAAI,KAAK,CACb,kCAAkC,QAAQ,4EAA4E,CACvH,CAAC;oBACJ,CAAC;oBAED,4DAA4D;oBAC5D,IACE,aAAa,YAAY,QAAQ;wBACjC,kGAAkG;wBAClG,aAAa,CAAC,WAAW,CAAC,IAAI,KAAK,eAAe,EAClD,CAAC;wBACD,MAAM,IAAI,KAAK,CACb,kCAAkC,QAAQ,oFAAoF,CAC/H,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAED,OAAO,WAAW,CAAC;YACrB,CAAC;YACD,UAAU,EAAE,QAAQ;YACpB,KAAK,EAAE,EAAE,EAAE,6DAA6D;YACxE,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,EAAE,EAAE,sHAAsH;SACrI,CAAC;QAEF,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,IAAI,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/C,SAAS;YACX,CAAC;YAED,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvC,IAAI,CAAC,qBAAqB,GAAG,QAAQ,CAAC,qBAAqB,CAAC;YAC5D,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;YACpC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC1B,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC;oBAC5B,IAAI,EAAE,UAAU;oBAChB,KAAK,EAAE,QAAQ,CAAC,WAAW;oBAC3B,QAAQ,EAAE,IAAI;oBACd,cAAc,EAAE,QAAQ;iBACzB,CAAC,CAAC;YACL,CAAC;YACD,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrB,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;YAClC,CAAC;YACD,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;YACvB,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/C,SAAS;YACX,CAAC;YAED,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,CAAC;YAC3D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;YACtB,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAC1B,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC;oBAC5B,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE,OAAO,CAAC,WAAW;oBAC1B,QAAQ,EAAE,IAAI;oBACd,aAAa,EAAE,OAAO;iBACvB,CAAC,CAAC;YACL,CAAC;YACD,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACpB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;YACjC,CAAC;YACD,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;YACtB,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,EAAE,CAAC;YAC3C,6EAA6E;YAC7E,6BAA6B;YAC7B,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,IAAI,UAAU,KAAK,MAAM,EAAE,CAAC;gBACjD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBACnC,gCAAgC;gBAChC,MAAM,KAAK,GAAG,SAAS,EAAE,OAAO,CAAC;gBACjC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;oBAClB,uEAAuE;oBACvE,OAAO,CAAC,IAAI,CACV,UAAU,QAAQ,4FAA4F,CAC/G,CAAC;oBACF,SAAS;gBACX,CAAC;gBACD,IAAI,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;oBAC3D,MAAM,IAAI,KAAK,CACb,kCAAkC,QAAQ,8BAA8B,OAAO,KAAK,6EAA6E,CAClK,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED;;;WAGG;QACH,KAAK,MAAM,KAAK,IAAI,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAClD,+FAA+F;YAC/F,MAAM,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YAExD,0EAA0E;YAC1E,IAAI,SAAS,GAAG,aAAa,CAAC;YAE9B,KAAK,MAAM,IAAI,IAAI,iBAAiB,EAAE,CAAC;gBACrC,IAAI,YAAY,GAAG,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAEtD,oCAAoC;gBACpC,IAAI,CAAC,YAAY,EAAE,CAAC;oBAClB,YAAY,GAAG;wBACb,KAAK,EAAE,IAAI,GAAG,EAAE;wBAChB,cAAc,EAAE,IAAI,GAAG,EAAE;qBAC1B,CAAC;oBACF,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;gBACnD,CAAC;gBAED,SAAS,GAAG,YAAY,CAAC;YAC3B,CAAC;YAED,gCAAgC;YAChC,IAAI,GAAG,EAAE,GAAG,IAAI,EAAE,KAAK,EAAE,CAAC;YAE1B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,SAAS,CAAC,MAAM,KAAK,EAAE,CAAC;gBACxB,MAAM,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACpD,IAAI,QAAQ,EAAE,CAAC;oBACb,2CAA2C;oBAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;wBAC1C,MAAM,IAAI,KAAK,CACb,gBAAgB,QAAQ,UAAU,QAAQ,CAAC,UAAU,6BAA6B,KAAK,yCAAyC,CACjI,CAAC;oBACJ,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;oBACpC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;gBAC5C,CAAC;YACH,CAAC;iBAAM,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACtB,MAAM,OAAO,GAAG,GAAG,KAAK,MAAM,CAAC;gBAC/B,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAEzC,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,KAAK,GAAG,EAAE,CAAC;oBACX,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBACtC,CAAC;gBAED,iEAAiE;gBACjE,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAE1B,IAAI,QAAQ,EAAE,CAAC;oBACb,2CAA2C;oBAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;wBAC1C,MAAM,IAAI,KAAK,CACb,uBAAuB,QAAQ,UAAU,QAAQ,CAAC,UAAU,6BAA6B,KAAK,yCAAyC,CACxI,CAAC;oBACJ,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;gBAClB,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;gBAEvC,IAAI,CAAC,KAAK,EAAE,CAAC;oBACX,KAAK,GAAG,EAAE,CAAC;oBACX,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBACpC,CAAC;gBAED;;;;;mBAKG;gBACH,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBACzC,IAAI,QAAQ,EAAE,CAAC;oBACb,2CAA2C;oBAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;wBAC1C,MAAM,IAAI,KAAK,CACb,oBAAoB,QAAQ,UAAU,QAAQ,CAAC,UAAU,6BAA6B,KAAK,yCAAyC,CACrI,CAAC;oBACJ,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,SAAS,KAAK,IAAI,CAAC;oBACnB,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;gBACjC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED,sEAAsE;IACtE,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;OAGG;IACH,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC;QAC1B,aAAa,CAAC,MAAM,GAAG;YACrB,OAAO,CAAC,cAAc,CAAC;gBACrB,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,EAAE;aACV,CAAC;SACH,CAAC;IACJ,CAAC;IAED,gDAAgD;IAChD,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3B,IAAI,SAAS,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;YAC3C,kBAAkB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAC7C,CAAC;QACD,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;YAC/B,mBAAmB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IACD,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,SAAS,uBAAuB,CAAC,IAAY;IAC3C,4EAA4E;IAC5E,OAAO,CACL,IAAA,qCAA0B,EAAC,IAAA,+BAAoB,EAAC,IAAI,CAAC,CAAC;QACpD,yBAAyB;SACxB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CACtB,CAAC;AACJ,CAAC;AAED,SAAS,+CAA+C,CAAC,IAAY;IACnE,OAAO,IAAA,yCAA8B,EAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAC;AACvE,CAAC;AAED,sDAAsD;AACtD,SAAS,qCAAqC,CAAC,MAAc;IAC3D,MAAM,IAAI,GAAG,uBAAuB,CAAC,MAAM,CAAC,CAAC;IAC7C,MAAM,MAAM,GAAG,IAAI,CAAC;IACpB,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,iCAAiC;IACtF,OAAO,GAAG,MAAM,GAAG,IAAI,GAAG,MAAM,EAAE,CAAC;AACrC,CAAC;AAED;;GAEG;AACH,SAAS,4BAA4B,CACnC,SAAwB,EACxB,OAAgB;AAChB,oDAAoD;AACpD,MAAkB;AAClB,8CAA8C;AAC9C,YAAY,GAAG,EAAE;IAEjB;;OAEG;IACH,IAAI,SAAS,CAAC,MAAM,EAAE,CAAC;QACrB,MAAM,cAAc,GAAG,MAAM,CAAC;QAC9B,MAAM,GAAG,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAE3C,8CAA8C;QAC9C,IAAI,cAAc,EAAE,CAAC;YACnB,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,OAAO,CAAC,uBAAuB,EAAE,CAAC;YACpC,OAAQ,MAAc,CAAC,SAAS,CAAC;QACnC,CAAC;QAED,sFAAsF;QACtF,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QACxD,YAAY,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAEtD,6EAA6E;QAC7E,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC;QACxB,MAAM,CAAC,OAAO,GAAG,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,CAAC;IAED,oGAAoG;IACpG,IAAI,CAAC,MAAM;QAAE,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;IAE9E,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;QAC9C,MAAM,SAAS,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC;QAE1C,wFAAwF;QACxF,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;QAC5D,SAAS,CAAC,OAAO,GAAG,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAErD,IAAI,OAAO,CAAC,uBAAuB,EAAE,CAAC;YACpC,OAAQ,SAAiB,CAAC,SAAS,CAAC;QACtC,CAAC;QAED,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAClC,CAAC;IAED,yCAAyC;IACzC,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,CAAC;QACtD,4BAA4B,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IACrE,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,WAAW,CAClB,WAAmB,EACnB,OAAgB,EAChB,SAAyC,EACzC,QAAuC;IAEvC,0BAA0B;IAC1B,MAAM,GAAG,GAAG,IAAA,oCAAyB,EAAC,IAAA,+BAAoB,EAAC,WAAW,CAAC,CAAC,CAAC;IACzE,IAAI,KAAK,GAAG,GAAG,CAAC;IAEhB,MAAM,KAAK,GAAG,IAAA,+BAAoB,EAAC,WAAW,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC3D,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IACzC,MAAM,CAAC,yBAAyB,EAAE,iBAAiB,CAAC,GAClD,IAAA,oCAAyB,EAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAEjD,MAAM,QAAQ,GAAG,yBAAyB,KAAK,SAAS,CAAC;IACzD,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAE3D,IAAI,yBAAyB,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,yBAAyB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QACzF,MAAM,IAAI,KAAK,CAAC,iBAAiB,WAAW,2CAA2C,CAAC,CAAC;IAC3F,CAAC;IAED,uFAAuF;IACvF,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,yBAAyB,KAAK,YAAY,EAAE,CAAC;QACrF,MAAM,YAAY,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1E,MAAM,IAAI,KAAK,CACb,iBAAiB,WAAW,oEAAoE,YAAY,GAAG,CAChH,CAAC;IACJ,CAAC;IACD,IAAI,WAAW,GAAG,CAAC,CAAC;IAEpB,MAAM,oBAAoB,GAAG,cAAc,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IACnE,MAAM,iBAAiB,GAAG,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC;IAEzD,IAAI,oBAAoB,EAAE,CAAC;QACzB,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,4EAA4E;YAC5E,WAAW,GAAG,CAAC,CAAC,CAAC;QACnB,CAAC;aAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC7B,+DAA+D;YAC/D,0CAA0C;YAC1C,WAAW,GAAG,CAAC,CAAC,CAAC;QACnB,CAAC;aAAM,IAAI,iBAAiB,KAAK,OAAO,CAAC,QAAQ,EAAE,CAAC;YAClD,8FAA8F;YAC9F,WAAW,GAAG,CAAC,CAAC;QAClB,CAAC;aAAM,IAAI,iBAAiB,KAAK,QAAQ,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,EAAE,CAAC;YACxE,0DAA0D;YAC1D,WAAW,GAAG,CAAC,CAAC;QAClB,CAAC;aAAM,IAAI,iBAAiB,KAAK,OAAO,CAAC,QAAQ,EAAE,CAAC;YAClD,mGAAmG;YACnG,gDAAgD;YAChD,WAAW,GAAG,CAAC,CAAC,CAAC;QACnB,CAAC;QAED,IAAI,KAAK,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CACb,wDAAwD,iBAAiB,WAAW,WAAW,GAAG,CACnG,CAAC;QACJ,CAAC;QAED,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,iBAAiB,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;IAClE,CAAC;IAED,OAAO;QACL,KAAK;QACL,WAAW;QACX,QAAQ;QACR,KAAK;QACL,UAAU,EAAE,GAAG,IAAI,SAAS;QAC5B,SAAS,EAAE,GAAG,IAAI,QAAQ;KAC3B,CAAC;AACJ,CAAC;AAED;;;;GAIG;AACH,SAAgB,iBAAiB,CAAC,GAAW,EAAE,OAAoB,IAAI,GAAG,EAAE;IAC1E,MAAM,KAAK,GAAG,IAAA,8BAAmB,EAAC,GAAG,CAAC,CAAC;IAEvC,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACd,OAAO,IAAI,CAAC;IACd,CAAC;IACD,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAChC,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC;IAElC,IAAI,SAAS,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;QACrC,MAAM,IAAI,KAAK,CAAC,qDAAqD,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC;IAC/F,CAAC;IAED,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACxB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACd,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QAC3B,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;IAC5D,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,eAAe,CAAC,IAAY;IAC1C,MAAM,OAAO,GAAG,IAAI;SACjB,KAAK,CAAC,GAAG,CAAC;SACV,GAAG,CAAC,CAAC,IAAI,EAA4B,EAAE;QACtC,IAAI,IAAI,KAAK,YAAY,EAAE,CAAC;YAC1B,OAAO;gBACL,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,IAAI;gBACV,QAAQ,EAAE,IAAI;aACf,CAAC;QACJ,CAAC;QACD,OAAO,IAAA,2BAAgB,EAAC,IAAI,CAAC,IAAI,IAAI,CAAC;IACxC,CAAC,CAAC;SACD,MAAM,CAAC,CAAC,IAAI,EAA6B,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAEvD,OAAO,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC;AAC/C,CAAC;AAED,SAAS,kBAAkB,CAAC,SAAwB,EAAE,OAAgB;IACpE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;QAC/D,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,EAAE;YAC9B,OAAO,CAAC,cAAc,CAAC;gBACrB,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,UAAU;aAClB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED,SAAS,mBAAmB,CAAC,SAAwB,EAAE,OAAgB;IACrE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;QACjE,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,YAAY,EAAE;YAChC,OAAO,CAAC,cAAc,CAAC;gBACrB,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE,YAAY;aACpB,CAAC;SACH,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED,SAAS,aAAa,CAAC,IAAe,EAAE,OAAgB;IACtD;;;OAGG;IACH,wCAAwC;IACxC,MAAM,SAAS,GAAG,IAAA,6BAAkB,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACjD,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACtD,OAAO,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,SAAS,CAAC;IAC3D,CAAC,CAAC,CAAC;IACH,IAAI,MAAM,GAAG,kBAAkB,EAAE,KAAK,CAAC;IACvC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAChC,IAAI,MAAM,EAAE,iBAAiB,EAAE,CAAC;QAC9B,IAAI,CAAC;YACH,kGAAkG;YAClG,MAAM;gBACJ,MAAM,CAAC,iBAAiB,CAAC,MAAM,IAAI,MAAM,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,MAAM,CAAC;QAC3F,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,qCAAqC,CAAC,EAAE,CAAC;oBAChE,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACd,sHAAsH;YACtH,MAAM,6BAA6B,GACjC,MAAM,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,EAAE,MAAM;gBAC7C,MAAM,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,EAAE,gBAAgB,CAAC;YAE1D,MAAM,GAAG,6BAA6B,IAAI,MAAM,CAAC;QACnD,CAAC;IACH,CAAC;IAED,OAAO;QACL,GAAG,IAAI;QACP,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;QAC5C,QAAQ,EAAE,EAAE,EAAE,2CAA2C;QACzD,gBAAgB,EAAE,MAAM;KACzB,CAAC;AACJ,CAAC;AAED,SAAS,wCAAwC,CAC/C,IAAe,EACf,OAAgB,EAChB,cAAwB,EAAE;IAE1B,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC1B,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC;SAAM,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;QACpC,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,WAAW,EAAE,IAAI,CAAC,qBAAsB,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC;SAAM,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;QAClC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,WAAW,IAAI,CAAC,UAAU,qCAAqC,CAAC,CAAC;QACnF,CAAC;QAED,6DAA6D;QAC7D,WAAW,GAAG,CAAC,GAAG,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QAEhD;;;;;WAKG;QACH,MAAM,SAAS,GAAG,IAAA,yBAAc,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC7C,MAAM,kBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;YACtD,OAAO,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,KAAK,SAAS,CAAC;QAC3D,CAAC,CAAC,CAAC;QACH,IAAI,MAAM,GAAG,kBAAkB,EAAE,KAAK,CAAC;QACvC,wCAAwC;QACxC,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC;YACrC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAChC,IAAI,MAAM,EAAE,iBAAiB,EAAE,CAAC;gBAC9B,IAAI,CAAC;oBACH,kGAAkG;oBAClG,MAAM;wBACJ,MAAM,CAAC,iBAAiB,CAAC,MAAM,IAAI,MAAM,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,MAAM,CAAC;gBAC3F,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;wBAC3B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,qCAAqC,CAAC,EAAE,CAAC;4BAChE,MAAM,KAAK,CAAC;wBACd,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,IAAI,SAAS,EAAE,CAAC;oBACd,sHAAsH;oBACtH,MAAM,6BAA6B,GACjC,MAAM,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,EAAE,MAAM;wBAC7C,MAAM,CAAC,iBAAiB,EAAE,CAAC,SAAS,CAAC,EAAE,gBAAgB,CAAC;oBAE1D,MAAM,GAAG,6BAA6B,IAAI,MAAM,CAAC;gBACnD,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM,CAAC,CAAC;YAC1E,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,MAAM,iBAAiB,GAAG,IAAI,CAAC,QAAQ;qBACpC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC;qBACnC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC;qBAClC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAEd,IAAI,SAAS,EAAE,CAAC;oBACd,MAAM,IAAI,KAAK,CACb,UAAU,IAAI,CAAC,UAAU,wBAAwB,MAAM,iBAAiB,SAAS,0BAA0B,iBAAiB,EAAE,CAC/H,CAAC;gBACJ,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CACb,UAAU,IAAI,CAAC,UAAU,wBAAwB,MAAM,yBAAyB,iBAAiB,EAAE,CACpG,CAAC;gBACJ,CAAC;YACH,CAAC;YAED,2GAA2G;YAC3G,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;YAC/B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;QAC3C,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,wCAAwC,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;AACH,CAAC;AAED,SAAS,eAAe,CAAC,MAAmB;IAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAExC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;QACf,MAAM,IAAI,KAAK,CACb,YAAY,KAAK,CAAC,UAAU,sEAAsE,CACnG,CAAC;IACJ,CAAC;IAED,wFAAwF;IACxF,4CAA4C;IAC5C,OAAO,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACnC,CAAC", "sourcesContent": ["import type { DynamicConvention, RouteNode } from './Route';\nimport {\n  matchArrayGroupName,\n  matchDynamicName,\n  matchGroupName,\n  matchLastGroupName,\n  removeFileSystemDots,\n  removeFileSystemExtensions,\n  removeSupportedExtensions,\n  stripInvisibleSegmentsFromPath,\n} from './matchers';\nimport type { RequireContext } from './types';\nimport { shouldLinkExternally } from './utils/url';\n\nexport type Options = {\n  ignore?: RegExp[];\n  preserveApiRoutes?: boolean;\n  ignoreRequireErrors?: boolean;\n  ignoreEntryPoints?: boolean;\n  /* Used to simplify testing for toEqual() comparison */\n  internal_stripLoadRoute?: boolean;\n  /* Used to simplify by skipping the generated routes */\n  skipGenerated?: boolean;\n  /* Skip the generated not found route  */\n  notFound?: boolean;\n  importMode?: string;\n  platformRoutes?: boolean;\n  sitemap?: boolean;\n  platform?: string;\n  redirects?: RedirectConfig[];\n  rewrites?: RewriteConfig[];\n  /* Keep redirects as valid routes within the RouteConfig tree */\n  preserveRedirectAndRewrites?: boolean;\n\n  /** Get the system route for a location. Useful for shimming React Native imports in SSR environments. */\n  getSystemRoute: (\n    route: Pick<RouteNode, 'route' | 'type'> & {\n      defaults?: RouteNode;\n      redirectConfig?: RedirectConfig;\n      rewriteConfig?: RewriteConfig;\n    }\n  ) => RouteNode;\n};\n\ntype DirectoryNode = {\n  layout?: RouteNode[];\n  files: Map<string, RouteNode[]>;\n  subdirectories: Map<string, DirectoryNode>;\n};\n\nexport type RedirectConfig = {\n  source: string;\n  destination: string;\n  destinationContextKey: string;\n  permanent?: boolean;\n  methods?: string[];\n  external?: boolean;\n};\n\nexport type RewriteConfig = {\n  source: string;\n  destination: string;\n  destinationContextKey: string;\n  methods?: string[];\n};\n\nconst validPlatforms = new Set(['android', 'ios', 'native', 'web']);\n\n/**\n * Given a Metro context module, return an array of nested routes.\n *\n * This is a two step process:\n *  1. Convert the RequireContext keys (file paths) into a directory tree.\n *      - This should extrapolate array syntax into multiple routes\n *      - Routes are given a specificity score\n *  2. Flatten the directory tree into routes\n *      - Routes in directories without _layout files are hoisted to the nearest _layout\n *      - The name of the route is relative to the nearest _layout\n *      - If multiple routes have the same name, the most specific route is used\n */\nexport function getRoutes(contextModule: RequireContext, options: Options): RouteNode | null {\n  const directoryTree = getDirectoryTree(contextModule, options);\n\n  // If there are no routes\n  if (!directoryTree) {\n    return null;\n  }\n\n  const rootNode = flattenDirectoryTreeToRoutes(directoryTree, options);\n\n  if (!options.ignoreEntryPoints) {\n    crawlAndAppendInitialRoutesAndEntryFiles(rootNode, options);\n  }\n\n  return rootNode;\n}\n\n/**\n * Converts the RequireContext keys (file paths) into a directory tree.\n */\nfunction getDirectoryTree(contextModule: RequireContext, options: Options) {\n  const importMode = options.importMode || process.env.EXPO_ROUTER_IMPORT_MODE;\n\n  const ignoreList: RegExp[] = [/^\\.\\/\\+(html|native-intent)\\.[tj]sx?$/]; // Ignore the top level ./+html file\n\n  if (options.ignore) {\n    ignoreList.push(...options.ignore);\n  }\n  if (!options.preserveApiRoutes) {\n    ignoreList.push(/\\+api$/, /\\+api\\.[tj]sx?$/);\n  }\n\n  const rootDirectory: DirectoryNode = {\n    files: new Map(),\n    subdirectories: new Map(),\n  };\n\n  let hasRoutes = false;\n  let isValid = false;\n\n  const contextKeys = contextModule.keys();\n  const redirects: Record<string, RedirectConfig> = {};\n  const rewrites: Record<string, RewriteConfig> = {};\n\n  let validRedirectDestinations: { contextKey: string; nameWithoutInvisible: string }[] | undefined;\n\n  const getValidDestinations = () => {\n    // Loop over contexts once and cache the valid destinations\n    validRedirectDestinations ??= contextKeys.map((key) => {\n      return {\n        contextKey: key,\n        nameWithoutInvisible: getNameWithoutInvisibleSegmentsFromRedirectPath(\n          removeSupportedExtensions(key)\n        ),\n      };\n    });\n    return validRedirectDestinations;\n  };\n\n  // If we are keeping redirects as valid routes, then we need to add them to the contextKeys\n  // This is useful for generating a sitemap with redirects, or static site generation that includes redirects\n  if (options.preserveRedirectAndRewrites) {\n    if (options.redirects) {\n      for (const redirect of options.redirects) {\n        const sourceContextKey = getSourceContextKeyFromRedirectSource(redirect.source);\n        const sourceName = getNameFromRedirectPath(redirect.source);\n\n        const isExternalRedirect = shouldLinkExternally(redirect.destination);\n\n        const targetDestinationName = isExternalRedirect\n          ? redirect.destination\n          : getNameWithoutInvisibleSegmentsFromRedirectPath(redirect.destination);\n\n        if (ignoreList.some((regex) => regex.test(sourceContextKey))) {\n          continue;\n        }\n\n        const validDestination = isExternalRedirect\n          ? undefined\n          : getValidDestinations().find(\n              (key) => key.nameWithoutInvisible === targetDestinationName\n            );\n        const destination = isExternalRedirect\n          ? targetDestinationName\n          : validDestination?.nameWithoutInvisible;\n        const destinationContextKey = isExternalRedirect\n          ? targetDestinationName\n          : validDestination?.contextKey;\n\n        if (!destinationContextKey || destination === undefined) {\n          /*\n           * Only throw the error when we are preserving the api routes\n           * When doing a static export, API routes will not exist so the redirect destination may not exist.\n           * The desired behavior for this error is to warn the user when running `expo start`, so its ok if\n           * `expo export` swallows this error.\n           */\n          if (options.preserveApiRoutes) {\n            throw new Error(`Redirect destination \"${redirect.destination}\" does not exist.`);\n          }\n\n          continue;\n        }\n\n        contextKeys.push(sourceContextKey);\n        redirects[sourceName] = {\n          source: sourceName,\n          destination,\n          destinationContextKey,\n          permanent: Boolean(redirect.permanent),\n          external: isExternalRedirect,\n          methods: redirect.methods,\n        };\n      }\n    }\n\n    if (options.rewrites) {\n      for (const rewrite of options.rewrites) {\n        const sourceContextKey = getSourceContextKeyFromRedirectSource(rewrite.source);\n        const sourceName = getNameFromRedirectPath(rewrite.source);\n\n        const targetDestinationName = getNameFromRedirectPath(rewrite.destination);\n\n        if (ignoreList.some((regex) => regex.test(sourceContextKey))) {\n          continue;\n        }\n\n        const validDestination = getValidDestinations().find(\n          (key) => key.nameWithoutInvisible === targetDestinationName\n        );\n        const destination = validDestination?.nameWithoutInvisible;\n        const destinationContextKey = validDestination?.contextKey;\n\n        if (!destinationContextKey || destination === undefined) {\n          /*\n           * Only throw the error when we are preserving the api routes\n           * When doing a static export, API routes will not exist so the redirect destination may not exist.\n           * The desired behavior for this error is to warn the user when running `expo start`, so its ok if\n           * `expo export` swallows this error.\n           */\n          if (options.preserveApiRoutes) {\n            throw new Error(`Rewrite destination \"${rewrite.destination}\" does not exist.`);\n          }\n\n          continue;\n        }\n\n        contextKeys.push(sourceContextKey);\n        rewrites[sourceName] = {\n          source: sourceName,\n          destination,\n          destinationContextKey,\n          methods: rewrite.methods,\n        };\n      }\n    }\n  }\n\n  const processedRedirectsRewrites = new Set<string>();\n\n  for (const filePath of contextKeys) {\n    if (ignoreList.some((regex) => regex.test(filePath))) {\n      continue;\n    }\n\n    isValid = true;\n\n    const meta = getFileMeta(filePath, options, redirects, rewrites);\n\n    // This is a file that should be ignored. e.g maybe it has an invalid platform?\n    if (meta.specificity < 0) {\n      continue;\n    }\n\n    let node: RouteNode = {\n      type: meta.isApi ? 'api' : meta.isLayout ? 'layout' : 'route',\n      loadRoute() {\n        let routeModule: any;\n\n        if (options.ignoreRequireErrors) {\n          try {\n            routeModule = contextModule(filePath);\n          } catch {\n            routeModule = {};\n          }\n        } else {\n          routeModule = contextModule(filePath);\n        }\n\n        if (process.env.NODE_ENV === 'development' && importMode === 'sync') {\n          // In development mode, when async routes are disabled, add some extra error handling to improve the developer experience.\n          // This can be useful when you accidentally use an async function in a route file for the default export.\n          if (routeModule instanceof Promise) {\n            throw new Error(\n              `Route \"${filePath}\" cannot be a promise when async routes is disabled.`\n            );\n          }\n\n          const defaultExport = routeModule?.default;\n          if (defaultExport instanceof Promise) {\n            throw new Error(\n              `The default export from route \"${filePath}\" is a promise. Ensure the React Component does not use async or promises.`\n            );\n          }\n\n          // check if default is an async function without invoking it\n          if (\n            defaultExport instanceof Function &&\n            // This only works on web because Hermes support async functions so we have to transform them out.\n            defaultExport.constructor.name === 'AsyncFunction'\n          ) {\n            throw new Error(\n              `The default export from route \"${filePath}\" is an async function. Ensure the React Component does not use async or promises.`\n            );\n          }\n        }\n\n        return routeModule;\n      },\n      contextKey: filePath,\n      route: '', // This is overwritten during hoisting based upon the _layout\n      dynamic: null,\n      children: [], // While we are building the directory tree, we don't know the node's children just yet. This is added during hoisting\n    };\n\n    if (meta.isRedirect) {\n      if (processedRedirectsRewrites.has(meta.route)) {\n        continue;\n      }\n\n      const redirect = redirects[meta.route];\n      node.destinationContextKey = redirect.destinationContextKey;\n      node.permanent = redirect.permanent;\n      node.generated = true;\n      if (node.type === 'route') {\n        node = options.getSystemRoute({\n          type: 'redirect',\n          route: redirect.destination,\n          defaults: node,\n          redirectConfig: redirect,\n        });\n      }\n      if (redirect.methods) {\n        node.methods = redirect.methods;\n      }\n      node.type = 'redirect';\n      processedRedirectsRewrites.add(meta.route);\n    }\n\n    if (meta.isRewrite) {\n      if (processedRedirectsRewrites.has(meta.route)) {\n        continue;\n      }\n\n      const rewrite = rewrites[meta.route];\n      node.destinationContextKey = rewrite.destinationContextKey;\n      node.generated = true;\n      if (node.type === 'route') {\n        node = options.getSystemRoute({\n          type: 'rewrite',\n          route: rewrite.destination,\n          defaults: node,\n          rewriteConfig: rewrite,\n        });\n      }\n      if (rewrite.methods) {\n        node.methods = rewrite.methods;\n      }\n      node.type = 'rewrite';\n      processedRedirectsRewrites.add(meta.route);\n    }\n\n    if (process.env.NODE_ENV === 'development') {\n      // If the user has set the `EXPO_ROUTER_IMPORT_MODE` to `sync` then we should\n      // filter the missing routes.\n      if (node.type !== 'api' && importMode === 'sync') {\n        const routeItem = node.loadRoute();\n        // Have a warning for nullish ex\n        const route = routeItem?.default;\n        if (route == null) {\n          // Do not throw an error since a user may just be creating a new route.\n          console.warn(\n            `Route \"${filePath}\" is missing the required default export. Ensure a React component is exported as default.`\n          );\n          continue;\n        }\n        if (['boolean', 'number', 'string'].includes(typeof route)) {\n          throw new Error(\n            `The default export from route \"${filePath}\" is an unsupported type: \"${typeof route}\". Only React Components are supported as default exports from route files.`\n          );\n        }\n      }\n    }\n\n    /**\n     * A single filepath may be extrapolated into multiple routes if it contains array syntax.\n     * Another way to thinking about is that a filepath node is present in multiple leaves of the directory tree.\n     */\n    for (const route of extrapolateGroups(meta.route)) {\n      // Traverse the directory tree to its leaf node, creating any missing directories along the way\n      const subdirectoryParts = route.split('/').slice(0, -1);\n\n      // Start at the root directory and traverse the path to the leaf directory\n      let directory = rootDirectory;\n\n      for (const part of subdirectoryParts) {\n        let subDirectory = directory.subdirectories.get(part);\n\n        // Create any missing subdirectories\n        if (!subDirectory) {\n          subDirectory = {\n            files: new Map(),\n            subdirectories: new Map(),\n          };\n          directory.subdirectories.set(part, subDirectory);\n        }\n\n        directory = subDirectory;\n      }\n\n      // Clone the node for this route\n      node = { ...node, route };\n\n      if (meta.isLayout) {\n        directory.layout ??= [];\n        const existing = directory.layout[meta.specificity];\n        if (existing) {\n          // In production, use the first route found\n          if (process.env.NODE_ENV !== 'production') {\n            throw new Error(\n              `The layouts \"${filePath}\" and \"${existing.contextKey}\" conflict on the route \"/${route}\". Remove or rename one of these files.`\n            );\n          }\n        } else {\n          node = getLayoutNode(node, options);\n          directory.layout[meta.specificity] = node;\n        }\n      } else if (meta.isApi) {\n        const fileKey = `${route}+api`;\n        let nodes = directory.files.get(fileKey);\n\n        if (!nodes) {\n          nodes = [];\n          directory.files.set(fileKey, nodes);\n        }\n\n        // API Routes have no specificity, they are always the first node\n        const existing = nodes[0];\n\n        if (existing) {\n          // In production, use the first route found\n          if (process.env.NODE_ENV !== 'production') {\n            throw new Error(\n              `The API route file \"${filePath}\" and \"${existing.contextKey}\" conflict on the route \"/${route}\". Remove or rename one of these files.`\n            );\n          }\n        } else {\n          nodes[0] = node;\n        }\n      } else {\n        let nodes = directory.files.get(route);\n\n        if (!nodes) {\n          nodes = [];\n          directory.files.set(route, nodes);\n        }\n\n        /**\n         * If there is an existing node with the same specificity, then we have a conflict.\n         * NOTE(Platform Routes):\n         *    We cannot check for specificity conflicts here, as we haven't processed all the context keys yet!\n         *    This will be checked during hoisting, as well as enforcing that all routes have a non-platform route.\n         */\n        const existing = nodes[meta.specificity];\n        if (existing) {\n          // In production, use the first route found\n          if (process.env.NODE_ENV !== 'production') {\n            throw new Error(\n              `The route files \"${filePath}\" and \"${existing.contextKey}\" conflict on the route \"/${route}\". Remove or rename one of these files.`\n            );\n          }\n        } else {\n          hasRoutes ||= true;\n          nodes[meta.specificity] = node;\n        }\n      }\n    }\n  }\n\n  // If there are no routes/layouts then we should display the tutorial.\n  if (!isValid) {\n    return null;\n  }\n\n  /**\n   * If there are no top-level _layout, add a default _layout\n   * While this is a generated route, it will still be generated even if skipGenerated is true.\n   */\n  if (!rootDirectory.layout) {\n    rootDirectory.layout = [\n      options.getSystemRoute({\n        type: 'layout',\n        route: '',\n      }),\n    ];\n  }\n\n  // Only include the sitemap if there are routes.\n  if (!options.skipGenerated) {\n    if (hasRoutes && options.sitemap !== false) {\n      appendSitemapRoute(rootDirectory, options);\n    }\n    if (options.notFound !== false) {\n      appendNotFoundRoute(rootDirectory, options);\n    }\n  }\n  return rootDirectory;\n}\n\nfunction getNameFromRedirectPath(path: string): string {\n  // Removing only the filesystem extensions, to be able to handle +api, +html\n  return (\n    removeFileSystemExtensions(removeFileSystemDots(path))\n      // Remove the leading `/`\n      .replace(/^\\//, '')\n  );\n}\n\nfunction getNameWithoutInvisibleSegmentsFromRedirectPath(path: string): string {\n  return stripInvisibleSegmentsFromPath(getNameFromRedirectPath(path));\n}\n\n// Creates fake context key for redirects and rewrites\nfunction getSourceContextKeyFromRedirectSource(source: string): string {\n  const name = getNameFromRedirectPath(source);\n  const prefix = './';\n  const suffix = /\\.[tj]sx?$/.test(name) ? '' : '.js'; // Ensure it has a file extension\n  return `${prefix}${name}${suffix}`;\n}\n\n/**\n * Flatten the directory tree into routes, hoisting routes to the nearest _layout.\n */\nfunction flattenDirectoryTreeToRoutes(\n  directory: DirectoryNode,\n  options: Options,\n  /* The nearest _layout file in the directory tree */\n  layout?: RouteNode,\n  /* Route names are relative to their layout */\n  pathToRemove = ''\n) {\n  /**\n   * This directory has a _layout file so it becomes the new target for hoisting routes.\n   */\n  if (directory.layout) {\n    const previousLayout = layout;\n    layout = getMostSpecific(directory.layout);\n\n    // Add the new layout as a child of its parent\n    if (previousLayout) {\n      previousLayout.children.push(layout);\n    }\n\n    if (options.internal_stripLoadRoute) {\n      delete (layout as any).loadRoute;\n    }\n\n    // `route` is the absolute pathname. We need to make this relative to the last _layout\n    const newRoute = layout.route.replace(pathToRemove, '');\n    pathToRemove = layout.route ? `${layout.route}/` : '';\n\n    // Now update this layout with the new relative route and dynamic conventions\n    layout.route = newRoute;\n    layout.dynamic = generateDynamic(layout.contextKey.slice(0));\n  }\n\n  // This should never occur as there will always be a root layout, but it makes the type system happy\n  if (!layout) throw new Error('Expo Router Internal Error: No nearest layout');\n\n  for (const routes of directory.files.values()) {\n    const routeNode = getMostSpecific(routes);\n\n    // `route` is the absolute pathname. We need to make this relative to the nearest layout\n    routeNode.route = routeNode.route.replace(pathToRemove, '');\n    routeNode.dynamic = generateDynamic(routeNode.route);\n\n    if (options.internal_stripLoadRoute) {\n      delete (routeNode as any).loadRoute;\n    }\n\n    layout.children.push(routeNode);\n  }\n\n  // Recursively flatten the subdirectories\n  for (const child of directory.subdirectories.values()) {\n    flattenDirectoryTreeToRoutes(child, options, layout, pathToRemove);\n  }\n\n  return layout;\n}\n\nfunction getFileMeta(\n  originalKey: string,\n  options: Options,\n  redirects: Record<string, RedirectConfig>,\n  rewrites: Record<string, RewriteConfig>\n) {\n  // Remove the leading `./`\n  const key = removeSupportedExtensions(removeFileSystemDots(originalKey));\n  let route = key;\n\n  const parts = removeFileSystemDots(originalKey).split('/');\n  const filename = parts[parts.length - 1];\n  const [filenameWithoutExtensions, platformExtension] =\n    removeSupportedExtensions(filename).split('.');\n\n  const isLayout = filenameWithoutExtensions === '_layout';\n  const isApi = originalKey.match(/\\+api\\.(\\w+\\.)?[jt]sx?$/);\n\n  if (filenameWithoutExtensions.startsWith('(') && filenameWithoutExtensions.endsWith(')')) {\n    throw new Error(`Invalid route ${originalKey}. Routes cannot end with '(group)' syntax`);\n  }\n\n  // Nested routes cannot start with the '+' character, except for the '+not-found' route\n  if (!isApi && filename.startsWith('+') && filenameWithoutExtensions !== '+not-found') {\n    const renamedRoute = [...parts.slice(0, -1), filename.slice(1)].join('/');\n    throw new Error(\n      `Invalid route ${originalKey}. Route nodes cannot start with the '+' character. \"Rename it to ${renamedRoute}\"`\n    );\n  }\n  let specificity = 0;\n\n  const hasPlatformExtension = validPlatforms.has(platformExtension);\n  const usePlatformRoutes = options.platformRoutes ?? true;\n\n  if (hasPlatformExtension) {\n    if (!usePlatformRoutes) {\n      // If the user has disabled platform routes, then we should ignore this file\n      specificity = -1;\n    } else if (!options.platform) {\n      // If we don't have a platform, then we should ignore this file\n      // This used by typed routes, sitemap, etc\n      specificity = -1;\n    } else if (platformExtension === options.platform) {\n      // If the platform extension is the same as the options.platform, then it is the most specific\n      specificity = 2;\n    } else if (platformExtension === 'native' && options.platform !== 'web') {\n      // `native` is allow but isn't as specific as the platform\n      specificity = 1;\n    } else if (platformExtension !== options.platform) {\n      // Somehow we have a platform extension that doesn't match the options.platform and it isn't native\n      // This is an invalid file and we will ignore it\n      specificity = -1;\n    }\n\n    if (isApi && specificity !== 0) {\n      throw new Error(\n        `API routes cannot have platform extensions. Remove '.${platformExtension}' from '${originalKey}'`\n      );\n    }\n\n    route = route.replace(new RegExp(`.${platformExtension}$`), '');\n  }\n\n  return {\n    route,\n    specificity,\n    isLayout,\n    isApi,\n    isRedirect: key in redirects,\n    isRewrite: key in rewrites,\n  };\n}\n\n/**\n * Generates a set of strings which have the router array syntax extrapolated.\n *\n * /(a,b)/(c,d)/e.tsx => new Set(['a/c/e.tsx', 'a/d/e.tsx', 'b/c/e.tsx', 'b/d/e.tsx'])\n */\nexport function extrapolateGroups(key: string, keys: Set<string> = new Set()): Set<string> {\n  const match = matchArrayGroupName(key);\n\n  if (!match) {\n    keys.add(key);\n    return keys;\n  }\n  const groups = match.split(',');\n  const groupsSet = new Set(groups);\n\n  if (groupsSet.size !== groups.length) {\n    throw new Error(`Array syntax cannot contain duplicate group name \"${groups}\" in \"${key}\".`);\n  }\n\n  if (groups.length === 1) {\n    keys.add(key);\n    return keys;\n  }\n\n  for (const group of groups) {\n    extrapolateGroups(key.replace(match, group.trim()), keys);\n  }\n\n  return keys;\n}\n\nexport function generateDynamic(path: string): DynamicConvention[] | null {\n  const dynamic = path\n    .split('/')\n    .map((part): DynamicConvention | null => {\n      if (part === '+not-found') {\n        return {\n          name: '+not-found',\n          deep: true,\n          notFound: true,\n        };\n      }\n      return matchDynamicName(part) ?? null;\n    })\n    .filter((part): part is DynamicConvention => !!part);\n\n  return dynamic.length === 0 ? null : dynamic;\n}\n\nfunction appendSitemapRoute(directory: DirectoryNode, options: Options) {\n  if (!directory.files.has('_sitemap') && options.getSystemRoute) {\n    directory.files.set('_sitemap', [\n      options.getSystemRoute({\n        type: 'route',\n        route: '_sitemap',\n      }),\n    ]);\n  }\n}\n\nfunction appendNotFoundRoute(directory: DirectoryNode, options: Options) {\n  if (!directory.files.has('+not-found') && options.getSystemRoute) {\n    directory.files.set('+not-found', [\n      options.getSystemRoute({\n        type: 'route',\n        route: '+not-found',\n      }),\n    ]);\n  }\n}\n\nfunction getLayoutNode(node: RouteNode, options: Options) {\n  /**\n   * A file called `(a,b)/(c)/_layout.tsx` will generate two _layout routes: `(a)/(c)/_layout` and `(b)/(c)/_layout`.\n   * Each of these layouts will have a different anchor based upon the first group name.\n   */\n  // We may strip loadRoute during testing\n  const groupName = matchLastGroupName(node.route);\n  const childMatchingGroup = node.children.find((child) => {\n    return child.route.replace(/\\/index$/, '') === groupName;\n  });\n  let anchor = childMatchingGroup?.route;\n  const loaded = node.loadRoute();\n  if (loaded?.unstable_settings) {\n    try {\n      // Allow unstable_settings={ initialRouteName: '...' } to override the default initial route name.\n      anchor =\n        loaded.unstable_settings.anchor ?? loaded.unstable_settings.initialRouteName ?? anchor;\n    } catch (error: any) {\n      if (error instanceof Error) {\n        if (!error.message.match(/You cannot dot into a client module/)) {\n          throw error;\n        }\n      }\n    }\n\n    if (groupName) {\n      // Allow unstable_settings={ 'custom': { initialRouteName: '...' } } to override the less specific initial route name.\n      const groupSpecificInitialRouteName =\n        loaded.unstable_settings?.[groupName]?.anchor ??\n        loaded.unstable_settings?.[groupName]?.initialRouteName;\n\n      anchor = groupSpecificInitialRouteName ?? anchor;\n    }\n  }\n\n  return {\n    ...node,\n    route: node.route.replace(/\\/?_layout$/, ''),\n    children: [], // Each layout should have its own children\n    initialRouteName: anchor,\n  };\n}\n\nfunction crawlAndAppendInitialRoutesAndEntryFiles(\n  node: RouteNode,\n  options: Options,\n  entryPoints: string[] = []\n) {\n  if (node.type === 'route') {\n    node.entryPoints = [...new Set([...entryPoints, node.contextKey])];\n  } else if (node.type === 'redirect') {\n    node.entryPoints = [...new Set([...entryPoints, node.destinationContextKey!])];\n  } else if (node.type === 'layout') {\n    if (!node.children) {\n      throw new Error(`Layout \"${node.contextKey}\" does not contain any child routes`);\n    }\n\n    // Every node below this layout will have it as an entryPoint\n    entryPoints = [...entryPoints, node.contextKey];\n\n    /**\n     * Calculate the initialRouteNode\n     *\n     * A file called `(a,b)/(c)/_layout.tsx` will generate two _layout routes: `(a)/(c)/_layout` and `(b)/(c)/_layout`.\n     * Each of these layouts will have a different anchor based upon the first group.\n     */\n    const groupName = matchGroupName(node.route);\n    const childMatchingGroup = node.children.find((child) => {\n      return child.route.replace(/\\/index$/, '') === groupName;\n    });\n    let anchor = childMatchingGroup?.route;\n    // We may strip loadRoute during testing\n    if (!options.internal_stripLoadRoute) {\n      const loaded = node.loadRoute();\n      if (loaded?.unstable_settings) {\n        try {\n          // Allow unstable_settings={ initialRouteName: '...' } to override the default initial route name.\n          anchor =\n            loaded.unstable_settings.anchor ?? loaded.unstable_settings.initialRouteName ?? anchor;\n        } catch (error: any) {\n          if (error instanceof Error) {\n            if (!error.message.match(/You cannot dot into a client module/)) {\n              throw error;\n            }\n          }\n        }\n\n        if (groupName) {\n          // Allow unstable_settings={ 'custom': { initialRouteName: '...' } } to override the less specific initial route name.\n          const groupSpecificInitialRouteName =\n            loaded.unstable_settings?.[groupName]?.anchor ??\n            loaded.unstable_settings?.[groupName]?.initialRouteName;\n\n          anchor = groupSpecificInitialRouteName ?? anchor;\n        }\n      }\n    }\n\n    if (anchor) {\n      const anchorRoute = node.children.find((child) => child.route === anchor);\n      if (!anchorRoute) {\n        const validAnchorRoutes = node.children\n          .filter((child) => !child.generated)\n          .map((child) => `'${child.route}'`)\n          .join(', ');\n\n        if (groupName) {\n          throw new Error(\n            `Layout ${node.contextKey} has invalid anchor '${anchor}' for group '(${groupName})'. Valid options are: ${validAnchorRoutes}`\n          );\n        } else {\n          throw new Error(\n            `Layout ${node.contextKey} has invalid anchor '${anchor}'. Valid options are: ${validAnchorRoutes}`\n          );\n        }\n      }\n\n      // Navigators can add initialsRoutes into the history, so they need to be to be included in the entryPoints\n      node.initialRouteName = anchor;\n      entryPoints.push(anchorRoute.contextKey);\n    }\n\n    for (const child of node.children) {\n      crawlAndAppendInitialRoutesAndEntryFiles(child, options, entryPoints);\n    }\n  }\n}\n\nfunction getMostSpecific(routes: RouteNode[]) {\n  const route = routes[routes.length - 1];\n\n  if (!routes[0]) {\n    throw new Error(\n      `The file ${route.contextKey} does not have a fallback sibling file without a platform extension.`\n    );\n  }\n\n  // This works even tho routes is holey array (e.g it might have index 0 and 2 but not 1)\n  // `.length` includes the holes in its count\n  return routes[routes.length - 1];\n}\n"]}