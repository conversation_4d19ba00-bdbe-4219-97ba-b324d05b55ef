{"version": 3, "file": "Route.js", "sourceRoot": "", "sources": ["../src/Route.tsx"], "names": [], "mappings": ";AAAA,YAAY,CAAC;;;AA2Db,oCAEC;AAED,sCAMC;AAQD,sBAMC;AAjFD,iCAAuF;AAEvF,yCAA2C;AAC3C,6CAAiE;AAgFxD,sGAhFA,kCAAqB,OAgFA;AAAE,2FAhFA,uBAAU,OAgFA;AApC1C,MAAM,mBAAmB,GAAG,IAAA,qBAAa,EAAmB,IAAI,CAAC,CAAC;AACrD,QAAA,uBAAuB,GAAG,IAAA,qBAAa,EAElD,EAAE,CAAC,CAAC;AAEN,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;IAC1C,mBAAmB,CAAC,WAAW,GAAG,WAAW,CAAC;AAChD,CAAC;AAED,+DAA+D;AAC/D,SAAgB,YAAY;IAC1B,OAAO,IAAA,WAAG,EAAC,mBAAmB,CAAC,CAAC;AAClC,CAAC;AAED,SAAgB,aAAa;IAC3B,MAAM,IAAI,GAAG,YAAY,EAAE,CAAC;IAC5B,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;QACjB,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;IAC7E,CAAC;IACD,OAAO,IAAA,wBAAa,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACxC,CAAC;AAOD,iEAAiE;AACjE,SAAgB,KAAK,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAc;IACzD,OAAO,CACL,CAAC,+BAAuB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,MAAM,CAAC,CACrD;MAAA,CAAC,mBAAmB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,mBAAmB,CAAC,QAAQ,CACrF;IAAA,EAAE,+BAAuB,CAAC,QAAQ,CAAC,CACpC,CAAC;AACJ,CAAC", "sourcesContent": ["'use client';\n\nimport { createContext, use, type ComponentType, type PropsWithChildren } from 'react';\n\nimport { getContextKey } from './matchers';\nimport { sortRoutesWithInitial, sortRoutes } from './sortRoutes';\nimport { type ErrorBoundaryProps } from './views/Try';\n\nexport type DynamicConvention = { name: string; deep: boolean; notFound?: boolean };\n\nexport type LoadedRoute = {\n  ErrorBoundary?: ComponentType<ErrorBoundaryProps>;\n  default?: ComponentType<any>;\n  unstable_settings?: Record<string, any>;\n  getNavOptions?: (args: any) => any;\n  generateStaticParams?: (props: {\n    params?: Record<string, string | string[]>;\n  }) => Record<string, string | string[]>[];\n};\n\nexport type RouteNode = {\n  /** The type of RouteNode */\n  type: 'route' | 'api' | 'layout' | 'redirect' | 'rewrite';\n  /** Load a route into memory. Returns the exports from a route. */\n  loadRoute: () => Partial<LoadedRoute>;\n  /** Loaded initial route name. */\n  initialRouteName?: string;\n  /** Nested routes */\n  children: RouteNode[];\n  /** Is the route a dynamic path */\n  dynamic: null | DynamicConvention[];\n  /** `index`, `error-boundary`, etc. */\n  route: string;\n  /** Context Module ID, used for matching children. */\n  contextKey: string;\n  /** Redirect Context Module ID, used for matching children. */\n  destinationContextKey?: string;\n  /** Is the redirect permanent. */\n  permanent?: boolean;\n  /** Added in-memory */\n  generated?: boolean;\n  /** Internal screens like the directory or the auto 404 should be marked as internal. */\n  internal?: boolean;\n  /** File paths for async entry modules that should be included in the initial chunk request to ensure the runtime JavaScript matches the statically rendered HTML representation. */\n  entryPoints?: string[];\n  /** HTTP methods for this route. If undefined, assumed to be ['GET'] */\n  methods?: string[];\n};\n\nconst CurrentRouteContext = createContext<RouteNode | null>(null);\nexport const LocalRouteParamsContext = createContext<\n  Record<string, string | undefined> | undefined\n>({});\n\nif (process.env.NODE_ENV !== 'production') {\n  CurrentRouteContext.displayName = 'RouteNode';\n}\n\n/** Return the RouteNode at the current contextual boundary. */\nexport function useRouteNode(): RouteNode | null {\n  return use(CurrentRouteContext);\n}\n\nexport function useContextKey(): string {\n  const node = useRouteNode();\n  if (node == null) {\n    throw new Error('No filename found. This is likely a bug in expo-router.');\n  }\n  return getContextKey(node.contextKey);\n}\n\nexport type RouteProps = PropsWithChildren<{\n  node: RouteNode;\n  route?: { params: Record<string, string | undefined> };\n}>;\n\n/** Provides the matching routes and filename to the children. */\nexport function Route({ children, node, route }: RouteProps) {\n  return (\n    <LocalRouteParamsContext.Provider value={route?.params}>\n      <CurrentRouteContext.Provider value={node}>{children}</CurrentRouteContext.Provider>\n    </LocalRouteParamsContext.Provider>\n  );\n}\n\nexport { sortRoutesWithInitial, sortRoutes };\n"]}