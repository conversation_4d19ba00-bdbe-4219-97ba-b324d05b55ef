{"version": 3, "file": "getLinkingConfig.js", "sourceRoot": "", "sources": ["../src/getLinkingConfig.ts"], "names": [], "mappings": ";;AAaA,kDASC;AAcD,4CAwEC;AA5GD,qDAA8E;AAC9E,yDAA6C;AAG7C,2CAAiD;AAEjD,yEAAsE;AACtE,6DAAsD;AAGtD,4CAA8F;AAG9F,SAAgB,mBAAmB,CAAC,MAAiB,EAAE,WAAoB,IAAI;IAC7E,OAAO;QACL,OAAO,EAAE;YACP,CAAC,8BAAkB,CAAC,EAAE;gBACpB,IAAI,EAAE,EAAE;gBACR,GAAG,IAAA,mDAAwB,EAAC,MAAM,EAAE,QAAQ,CAAC;aAC9C;SACF;KACF,CAAC;AACJ,CAAC;AAcD,SAAgB,gBAAgB,CAC9B,MAAiB,EACjB,OAAuB,EACvB,YAA6B,EAC7B,EAAE,QAAQ,GAAG,IAAI,EAAE,SAAS,EAAE,SAAS,KAA2B,EAAE;IAEpE,gHAAgH;IAChH,IAAI,mBAAmB,GAAG,KAAK,CAAC;IAChC,IAAI,UAAwD,CAAC;IAE7D,MAAM,gBAAgB,GAAG,OAAO;SAC7B,IAAI,EAAE;SACN,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,CAAC;IAC9D,MAAM,aAAa,GAA6B,gBAAgB;QAC9D,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC;QAC3B,CAAC,CAAC,SAAS,CAAC;IAEd,MAAM,MAAM,GAAG,mBAAmB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAErD,OAAO;QACL,QAAQ,EAAE,EAAE;QACZ,MAAM;QACN,8EAA8E;QAC9E,wEAAwE;QACxE,+EAA+E;QAC/E,8GAA8G;QAC9G,8EAA8E;QAC9E,aAAa;YACX,gHAAgH;YAChH,kCAAkC;YAClC,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACzB,IAAI,4BAAQ,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC;oBAC1B,UAAU,GAAG,SAAS,IAAI,IAAA,uBAAa,GAAE,CAAC;gBAC5C,CAAC;qBAAM,CAAC;oBACN,UAAU,GAAG,SAAS,IAAI,IAAA,uBAAa,GAAE,CAAC;oBAE1C,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE,CAAC;wBACnC,UAAU,GAAG,IAAA,mCAAc,EAAC,UAAU,EAAE,SAAS,CAAC,CAAC;wBACnD,IAAI,UAAU,IAAI,OAAO,aAAa,EAAE,kBAAkB,KAAK,UAAU,EAAE,CAAC;4BAC1E,UAAU,GAAG,aAAa,CAAC,kBAAkB,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;wBACrF,CAAC;oBACH,CAAC;yBAAM,IAAI,UAAU,EAAE,CAAC;wBACtB,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,EAAE;4BACnC,GAAG,GAAG,IAAA,mCAAc,EAAC,GAAG,EAAE,SAAS,CAAC,CAAC;4BACrC,IAAI,GAAG,IAAI,OAAO,aAAa,EAAE,kBAAkB,KAAK,UAAU,EAAE,CAAC;gCACnE,OAAO,aAAa,CAAC,kBAAkB,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;4BACxE,CAAC;4BACD,OAAO,GAAG,CAAC;wBACb,CAAC,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;gBACD,mBAAmB,GAAG,IAAI,CAAC;YAC7B,CAAC;YACD,OAAO,UAAU,CAAC;QACpB,CAAC;QACD,SAAS,EAAE,IAAA,mBAAS,EAAC,aAAa,EAAE,SAAS,CAAC;QAC9C,gBAAgB,EAAE,CAA2B,IAAY,EAAE,OAA4B,EAAE,EAAE;YACzF,OAAO,IAAA,0BAAgB,EAAC,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,QAAQ,CAAC,CAAC;QAClE,CAAC;QACD,gBAAgB,CAAC,KAAY,EAAE,OAA+C;YAC5E,OAAO,CACL,IAAA,0BAAgB,EAAC,KAAK,EAAE;gBACtB,GAAG,MAAM;gBACT,GAAG,OAAO;gBACV,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,OAAO,EAAE,OAAO,IAAI,EAAE;aAClD,CAAC,IAAI,GAAG,CACV,CAAC;QACJ,CAAC;QACD,gEAAgE;QAChE,kDAAkD;QAClD,kBAAkB,EAAlB,2BAAkB;KACnB,CAAC;AACJ,CAAC", "sourcesContent": ["import { getActionFromState, LinkingOptions } from '@react-navigation/native';\nimport { Platform } from 'expo-modules-core';\n\nimport { RouteNode } from './Route';\nimport { INTERNAL_SLOT_NAME } from './constants';\nimport { Options, State } from './fork/getPathFromState';\nimport { getReactNavigationConfig } from './getReactNavigationConfig';\nimport { applyRedirects } from './getRoutesRedirects';\nimport { UrlObject } from './global-state/routeInfo';\nimport type { StoreRedirects } from './global-state/router-store';\nimport { getInitialURL, getPathFromState, getStateFromPath, subscribe } from './link/linking';\nimport { NativeIntent, RequireContext } from './types';\n\nexport function getNavigationConfig(routes: RouteNode, metaOnly: boolean = true) {\n  return {\n    screens: {\n      [INTERNAL_SLOT_NAME]: {\n        path: '',\n        ...getReactNavigationConfig(routes, metaOnly),\n      },\n    },\n  };\n}\n\nexport type ExpoLinkingOptions<T extends object = Record<string, unknown>> = LinkingOptions<T> & {\n  getPathFromState: typeof getPathFromState;\n  getStateFromPath: typeof getStateFromPath;\n};\n\nexport type LinkingConfigOptions = {\n  metaOnly?: boolean;\n  serverUrl?: string;\n  getInitialURL?: typeof getInitialURL;\n  redirects?: StoreRedirects[];\n};\n\nexport function getLinkingConfig(\n  routes: RouteNode,\n  context: RequireContext,\n  getRouteInfo: () => UrlObject,\n  { metaOnly = true, serverUrl, redirects }: LinkingConfigOptions = {}\n): ExpoLinkingOptions {\n  // Returning `undefined` / `null from `getInitialURL` are valid values, so we need to track if it's been called.\n  let hasCachedInitialUrl = false;\n  let initialUrl: ReturnType<typeof getInitialURL> | undefined;\n\n  const nativeLinkingKey = context\n    .keys()\n    .find((key) => key.match(/^\\.\\/\\+native-intent\\.[tj]sx?$/));\n  const nativeLinking: NativeIntent | undefined = nativeLinkingKey\n    ? context(nativeLinkingKey)\n    : undefined;\n\n  const config = getNavigationConfig(routes, metaOnly);\n\n  return {\n    prefixes: [],\n    config,\n    // A custom getInitialURL is used on native to ensure the app always starts at\n    // the root path if it's launched from something other than a deep link.\n    // This helps keep the native functionality working like the web functionality.\n    // For example, if you had a root navigator where the first screen was `/settings` and the second was `/index`\n    // then `/index` would be used on web and `/settings` would be used on native.\n    getInitialURL() {\n      // Expo Router calls `getInitialURL` twice, which may confuse the user if they provide a custom `getInitialURL`.\n      // Therefor we memoize the result.\n      if (!hasCachedInitialUrl) {\n        if (Platform.OS === 'web') {\n          initialUrl = serverUrl ?? getInitialURL();\n        } else {\n          initialUrl = serverUrl ?? getInitialURL();\n\n          if (typeof initialUrl === 'string') {\n            initialUrl = applyRedirects(initialUrl, redirects);\n            if (initialUrl && typeof nativeLinking?.redirectSystemPath === 'function') {\n              initialUrl = nativeLinking.redirectSystemPath({ path: initialUrl, initial: true });\n            }\n          } else if (initialUrl) {\n            initialUrl = initialUrl.then((url) => {\n              url = applyRedirects(url, redirects);\n              if (url && typeof nativeLinking?.redirectSystemPath === 'function') {\n                return nativeLinking.redirectSystemPath({ path: url, initial: true });\n              }\n              return url;\n            });\n          }\n        }\n        hasCachedInitialUrl = true;\n      }\n      return initialUrl;\n    },\n    subscribe: subscribe(nativeLinking, redirects),\n    getStateFromPath: <ParamList extends object>(path: string, options?: Options<ParamList>) => {\n      return getStateFromPath(path, options, getRouteInfo().segments);\n    },\n    getPathFromState(state: State, options: Parameters<typeof getPathFromState>[1]) {\n      return (\n        getPathFromState(state, {\n          ...config,\n          ...options,\n          screens: config.screens ?? options?.screens ?? {},\n        }) ?? '/'\n      );\n    },\n    // Add all functions to ensure the types never need to fallback.\n    // This is a convenience for usage in the package.\n    getActionFromState,\n  };\n}\n"]}