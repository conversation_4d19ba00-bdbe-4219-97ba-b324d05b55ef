{"version": 3, "file": "NativeLinearGradient.js", "sourceRoot": "", "sources": ["../src/NativeLinearGradient.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAIpC,mEAAmE;AACnE,kFAAkF;AAClF,MAAM,CAAC,OAAO,UAAU,oBAAoB,CAAC,KAAgC;IAC3E,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,SAAS,EAAE,GAAG,KAAK,CAAC;IACxE,OAAO,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;IACjE,OAAO,CAAC,IAAI,CAAC,IAAK,SAAiB,CAAC,EAAG,CAAC;AAC1C,CAAC", "sourcesContent": ["import * as React from 'react';\nimport { View } from 'react-native';\n\nimport { NativeLinearGradientProps } from './NativeLinearGradient.types';\n\n// This is a shim view for platforms that aren't supported by Expo.\n// The component and prop types should match all of the other platform variations.\nexport default function NativeLinearGradient(props: NativeLinearGradientProps): React.ReactElement {\n  const { colors, locations, startPoint, endPoint, ...viewProps } = props;\n  console.warn('LinearGradient is not available on this platform');\n  return <View {...(viewProps as any)} />;\n}\n"]}