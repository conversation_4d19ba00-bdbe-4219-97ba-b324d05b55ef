{"name": "expo-modules-core", "version": "2.4.2", "description": "The core of Expo Modules architecture", "main": "src/index.ts", "types": "build/index.d.ts", "sideEffects": ["*.fx.ts"], "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module"}, "keywords": ["expo", "modules", "expo-modules", "unimodules", "react-native"], "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-modules-core"}, "bugs": {"url": "https://github.com/expo/expo/issues"}, "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://github.com/expo/expo/tree/main/packages/expo-modules-core", "jest": {"preset": "expo-module-scripts"}, "dependencies": {"invariant": "^2.2.4"}, "devDependencies": {"@testing-library/react-native": "^13.1.0", "expo-module-scripts": "^4.1.8"}, "gitHead": "d0d8a3fb9633f94037dd6d96e673e0698ab8b6e0"}