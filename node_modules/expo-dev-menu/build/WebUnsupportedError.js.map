{"version": 3, "file": "WebUnsupportedError.js", "sourceRoot": "", "sources": ["../src/WebUnsupportedError.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,OAAO,OAAO,mBAAoB,SAAQ,KAAK;IACpD;QACE,KAAK,CAAC,8CAA8C,CAAC,CAAC;IACxD,CAAC;CACF", "sourcesContent": ["export default class WebUnsupportedError extends Error {\n  constructor() {\n    super(\"`expo-dev-menu` isn't supported on Expo Web.\");\n  }\n}\n"]}