/**
 * Automatically generated by expo-modules-test-core.
 *
 * This autogenerated file provides a mock for native Expo module,
 * and works out of the box with the expo jest preset.
 *  */

export async function reload(): Promise<void> {}

export async function togglePerformanceMonitor(): Promise<void> {}

export async function toggleInspector(): Promise<void> {}

export async function openJSInspector(): Promise<void> {}

export async function toggleFastRefresh(): Promise<void> {}

export async function loadFontsAsync(): Promise<any> {}

export async function hideMenu(): Promise<any> {}

export async function closeMenu(): Promise<any> {}

export async function setOnboardingFinished(finished: boolean): Promise<any> {}

export async function openDevMenuFromReactNative(): Promise<any> {}

export async function onScreenChangeAsync(currentScreen: string | undefined): Promise<any> {}

export async function fireCallback(name: string): Promise<any> {}

export async function copyToClipboardAsync(content: string): Promise<any> {}
