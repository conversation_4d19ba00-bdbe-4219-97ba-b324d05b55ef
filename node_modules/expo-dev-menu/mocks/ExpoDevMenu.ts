/**
 * Automatically generated by expo-modules-test-core.
 *
 * This autogenerated file provides a mock for native Expo module,
 * and works out of the box with the expo jest preset.
 *  */

export async function openMenu(): Promise<any> {}

export async function closeMenu(): Promise<any> {}

export async function hideMenu(): Promise<any> {}

export async function addDevMenuCallbacks(
  callbacks: {
    [key: string]: any;
  }[]
): Promise<any> {}
