{"platforms": ["apple", "android"], "apple": {"podspecPath": "expo-dev-menu.podspec", "swiftModuleName": "EXDevMenu", "modules": ["DevMenuModule", "DevMenuInternalModule", "DevMenuPreferences", "RNCSafeAreaProviderManager"], "reactDelegateHandlers": ["ExpoDevMenuReactDelegateHandler"], "debugOnly": true}, "android": {"modules": ["expo.modules.devmenu.modules.DevMenuModule", "expo.modules.devmenu.modules.DevMenuPreferences"]}}