package expo.modules.devmenu

import com.facebook.react.bridge.Arguments
import com.facebook.react.bridge.ReadableMap
import com.facebook.react.bridge.WritableMap
import expo.interfaces.devmenu.DevMenuPreferencesInterface

open class DevMenuDefaultPreferences : DevMenuPreferencesInterface {
  private fun methodUnavailable() {
    throw NoSuchMethodError("You cannot change the default settings. Export `DevMenuSettings` module if you want to change the settings.")
  }

  override var motionGestureEnabled: Boolean
    get() = true
    set(_) = methodUnavailable()

  override var touchGestureEnabled: Boolean
    get() = true
    set(_) = methodUnavailable()

  override var keyCommandsEnabled: <PERSON>olean
    get() = true
    set(_) = methodUnavailable()

  override var showsAtLaunch: Boolean
    get() = false
    set(_) = methodUnavailable()

  override var isOnboardingFinished: Boolean
    get() = true
    set(_) = methodUnavailable()

  override fun serialize(): WritableMap =
    Arguments
      .createMap()
      .apply {
        putBoolean("motionGestureEnabled", motionGestureEnabled)
        putBoolean("touchGestureEnabled", touchGestureEnabled)
        putBoolean("keyCommandsEnabled", keyCommandsEnabled)
        putBoolean("showsAtLaunch", showsAtLaunch)
        putBoolean("isOnboardingFinished", isOnboardingFinished)
      }

  override fun setPreferences(settings: ReadableMap) {
    methodUnavailable()
  }
}
