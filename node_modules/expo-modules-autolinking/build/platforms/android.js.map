{"version": 3, "file": "android.js", "sourceRoot": "", "sources": ["../../src/platforms/android.ts"], "names": [], "mappings": ";;;;;AAcA,4CAMC;AAKD,4DAWC;AAED,4CAKC;AAED,gDA4EC;AAED,gFAaC;AAED,gEAaC;AAyGD,kEAEC;AAaD,sFAOC;AAUD,8DAcC;AA9SD,4CAAoB;AACpB,+BAA4B;AAC5B,gDAAwB;AASxB,MAAM,uBAAuB,GAAG,mBAAmB,CAAC;AACpD,MAAM,4BAA4B,GAAG,yBAAyB,CAAC;AAE/D,SAAgB,gBAAgB,CAAC,OAAuB;IACtD,MAAM,eAAe,GAAG,OAAO,CAAC,OAAO,EAAE,eAAe,CAAC;IACzD,IAAI,eAAe,EAAE,CAAC;QACpB,OAAO,EAAE,eAAe,EAAE,CAAC;IAC7B,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,wBAAwB,CAC5C,OAAkC,EAClC,UAAkB,EAClB,SAAiB;IAEjB,MAAM,oBAAoB,GAAG,MAAM,mCAAmC,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;IAC3F,MAAM,UAAU,GAAG,cAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAC5C,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;QAC/B,MAAM,YAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC3D,CAAC;IACD,MAAM,YAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,EAAE,oBAAoB,EAAE,MAAM,CAAC,CAAC;AACxE,CAAC;AAED,SAAgB,gBAAgB,CAAC,WAAmB;IAClD,OAAO,CACL,YAAE,CAAC,UAAU,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;QACrD,YAAE,CAAC,UAAU,CAAC,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC,CAC1D,CAAC;AACJ,CAAC;AAEM,KAAK,UAAU,kBAAkB,CACtC,WAAmB,EACnB,QAAyB;IAEzB,8EAA8E;IAE9E,0BAA0B;IAC1B,IAAI,WAAW,KAAK,kCAAkC,EAAE,CAAC;QACvD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,OAAO,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,oBAAoB,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,CACjE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,kBAAkB,EAAE,EAAE,EAAE,CAAC,CAAC;QACjD,EAAE;QACF,KAAK;QACL,SAAS,EAAE,cAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC;QAC9C,kBAAkB,EAAE,kBAAkB,IAAI,IAAI;KAC/C,CAAC,CACH,CAAC;IAEF,MAAM,kBAAkB,GAAG,2BAA2B,CAAC,WAAW,CAAC,CAAC;IAEpE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;QACrC,EAAE,eAAe,CAAC,kBAAkB,CAAC;QACrC,EAAE,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE;QACnB,OAAO,CAAC,OAAO,CAAC,SAAS,IAAI,gBAAgB,CAAC,cAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IACxF,CAAC,CAAC,CAAC;IAEL,uFAAuF;IACvF,IAAI,CAAC,eAAe,EAAE,MAAM,EAAE,CAAC;QAC7B,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO;YACL,WAAW;YACX,OAAO;SACR,CAAC;IACJ,CAAC;IAED,MAAM,QAAQ,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;QAC/C,MAAM,WAAW,GAAG,cAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QAE3D,MAAM,WAAW,GAAG,CAAC,OAAO,CAAC,iBAAiB,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YACxE,MAAM,WAAW,GAAG,GAAG,kBAAkB,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC;YAC/D,MAAM,UAAU,GAAG,cAAI,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;YAChE,OAAO;gBACL,IAAI,EAAE,WAAW;gBACjB,WAAW,EAAE,cAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,WAAW,CAAC;gBAC7D,UAAU;aACX,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;QAChC,MAAM,8BAA8B,GAAG,OAAO,CAAC,8BAA8B;YAC3E,CAAC,CAAC,cAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,8BAA8B,CAAC;YAClE,CAAC,CAAC,SAAS,CAAC;QAEd,OAAO;YACL,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,SAAS,EAAE,WAAW;YACtB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;YAC9B,GAAG,CAAC,8BAA8B,CAAC,CAAC,CAAC,EAAE,8BAA8B,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAC7E,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YACvC,GAAG,CAAC,WAAW,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SACpD,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;IAE3D,OAAO;QACL,WAAW;QACX,QAAQ;QACR,GAAG,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC3C,GAAG,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;KACrD,CAAC;AACJ,CAAC;AAEM,KAAK,UAAU,kCAAkC,CACtD,iBAAyB;IAEzB,MAAM,qBAAqB,GAAG,MAAM,0BAA0B,CAC5D,iBAAiB,EACjB,4BAA4B,CAC7B,CAAC;IACF,IAAI,qBAAqB,EAAE,CAAC;QAC1B,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;QAC3C,CAAC;QAAC,MAAM,CAAC,CAAA,CAAC;IACZ,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAEM,KAAK,UAAU,0BAA0B,CAC9C,iBAAyB,EACzB,WAAmB;IAEnB,MAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,uBAAuB,CAAC,CAAC;IACxE,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAC/D,MAAM,aAAa,GAAG,yBAAyB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;QACvE,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,aAAa,CAAC;QACvB,CAAC;IACH,CAAC;IAAC,MAAM,CAAC,CAAA,CAAC;IACV,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,mCAAmC,CAChD,OAAkC,EAClC,SAAiB;IAEjB,mHAAmH;IACnH,MAAM,eAAe,GAAG,MAAM,wBAAwB,CACpD,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,WAAW,KAAK,MAAM,CAAC,CAC1D,CAAC;IAEF,MAAM,cAAc,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,CAAC;IAEzD,OAAO,WAAW,SAAS;;;;;;;;;;;EAW3B,eAAe,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,aAAa,YAAY,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;;;;QAI1E,cAAc,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,SAAS,WAAW,QAAQ,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;;;;CAapF,CAAC;AACF,CAAC;AAED,SAAS,kBAAkB,CAAC,OAAkC;IAC5D,MAAM,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;IAEpE,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;IACnF,MAAM,UAAU,GAAI,EAAe,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;IAChG,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,KAAK,UAAU,wBAAwB,CAAC,OAAkC;IACxE,MAAM,OAAO,GAAa,EAAE,CAAC;IAE7B,MAAM,sBAAsB,GAAa,EAAE,CAAC;IAC5C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,KAAK,MAAM,OAAO,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE,EAAE,CAAC;YAC5C,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED,MAAM,OAAO,CAAC,GAAG,CACf,sBAAsB,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,EAAE;QAC7C,MAAM,KAAK,GAAG,MAAM,IAAA,WAAI,EAAC,uBAAuB,EAAE;YAChD,GAAG,EAAE,SAAS;SACf,CAAC,CAAC;QAEH,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,WAAW,GAAG,MAAM,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC;YAEnF,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE;gBACzB,IAAI,OAAO,CAAC,GAAG,CAAC,wCAAwC,EAAE,CAAC;oBACzD,OAAO,uEAAuE,CAAC;gBACjF,CAAC;qBAAM,CAAC;oBACN,OAAO,qEAAqE,CAAC;gBAC/E,CAAC;YACH,CAAC,CAAC,EAAE,CAAC;YAEL,6CAA6C;YAC7C,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC;gBACpC,SAAS;YACX,CAAC;YAED,MAAM,gBAAgB,GAAG,WAAW,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;YAEnE,IAAI,gBAAgB,EAAE,CAAC;gBACrB,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,cAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;gBACzD,OAAO,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,CAAC;YACrD,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CACH,CAAC;IACF,OAAO,OAAO,CAAC,IAAI,EAAE,CAAC;AACxB,CAAC;AAED;;;;;GAKG;AACH,SAAgB,2BAA2B,CAAC,WAAmB;IAC7D,OAAO,WAAW,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;AAC7D,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAgB,qCAAqC,CACnD,WAAmB,EACnB,eAAuB;IAEvB,MAAM,IAAI,GAAG,2BAA2B,CAAC,WAAW,CAAC,CAAC;IACtD,MAAM,OAAO,GAAG,cAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAClE,OAAO,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,OAAO,EAAE,CAAC;AAC7D,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,yBAAyB,CAAC,QAAgB,EAAE,YAAoB;IAC9E,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAC7B,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YAClC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAC9B,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAC/B,IAAI,GAAG,KAAK,YAAY,EAAE,CAAC;gBACzB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBAC/C,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["import fs from 'fs';\nimport { glob } from 'glob';\nimport path from 'path';\n\nimport type {\n  ExtraDependencies,\n  ModuleDescriptorAndroid,\n  PackageRevision,\n  ResolveOptions,\n} from '../types';\n\nconst ANDROID_PROPERTIES_FILE = 'gradle.properties';\nconst ANDROID_EXTRA_BUILD_DEPS_KEY = 'android.extraMavenRepos';\n\nexport function getConfiguration(options: ResolveOptions): Record<string, any> | undefined {\n  const buildFromSource = options.android?.buildFromSource;\n  if (buildFromSource) {\n    return { buildFromSource };\n  }\n  return undefined;\n}\n\n/**\n * Generates Java file that contains all autolinked packages.\n */\nexport async function generatePackageListAsync(\n  modules: ModuleDescriptorAndroid[],\n  targetPath: string,\n  namespace: string\n): Promise<void> {\n  const generatedFileContent = await generatePackageListFileContentAsync(modules, namespace);\n  const parentPath = path.dirname(targetPath);\n  if (!fs.existsSync(parentPath)) {\n    await fs.promises.mkdir(parentPath, { recursive: true });\n  }\n  await fs.promises.writeFile(targetPath, generatedFileContent, 'utf8');\n}\n\nexport function isAndroidProject(projectRoot: string): boolean {\n  return (\n    fs.existsSync(path.join(projectRoot, 'build.gradle')) ||\n    fs.existsSync(path.join(projectRoot, 'build.gradle.kts'))\n  );\n}\n\nexport async function resolveModuleAsync(\n  packageName: string,\n  revision: PackageRevision\n): Promise<ModuleDescriptorAndroid | null> {\n  // TODO: Relative source dir should be configurable through the module config.\n\n  // Don't link itself... :D\n  if (packageName === '@unimodules/react-native-adapter') {\n    return null;\n  }\n\n  const plugins = (revision.config?.androidGradlePlugins() ?? []).map(\n    ({ id, group, sourceDir, applyToRootProject }) => ({\n      id,\n      group,\n      sourceDir: path.join(revision.path, sourceDir),\n      applyToRootProject: applyToRootProject ?? true,\n    })\n  );\n\n  const defaultProjectName = convertPackageToProjectName(packageName);\n\n  const androidProjects = revision.config\n    ?.androidProjects(defaultProjectName)\n    ?.filter((project) => {\n      return !project.isDefault || isAndroidProject(path.join(revision.path, project.path));\n    });\n\n  // Just in case where the module doesn't have its own `build.gradle`/`settings.gradle`.\n  if (!androidProjects?.length) {\n    if (!plugins.length) {\n      return null;\n    }\n\n    return {\n      packageName,\n      plugins,\n    };\n  }\n\n  const projects = androidProjects.map((project) => {\n    const projectPath = path.join(revision.path, project.path);\n\n    const aarProjects = (project.gradleAarProjects ?? [])?.map((aarProject) => {\n      const projectName = `${defaultProjectName}$${aarProject.name}`;\n      const projectDir = path.join(projectPath, 'build', projectName);\n      return {\n        name: projectName,\n        aarFilePath: path.join(revision.path, aarProject.aarFilePath),\n        projectDir,\n      };\n    });\n\n    const { publication } = project;\n    const shouldUsePublicationScriptPath = project.shouldUsePublicationScriptPath\n      ? path.join(revision.path, project.shouldUsePublicationScriptPath)\n      : undefined;\n\n    return {\n      name: project.name,\n      sourceDir: projectPath,\n      modules: project.modules ?? [],\n      ...(shouldUsePublicationScriptPath ? { shouldUsePublicationScriptPath } : {}),\n      ...(publication ? { publication } : {}),\n      ...(aarProjects?.length > 0 ? { aarProjects } : {}),\n    };\n  });\n\n  const coreFeatures = revision.config?.coreFeatures() ?? [];\n\n  return {\n    packageName,\n    projects,\n    ...(plugins?.length > 0 ? { plugins } : {}),\n    ...(coreFeatures.length > 0 ? { coreFeatures } : {}),\n  };\n}\n\nexport async function resolveExtraBuildDependenciesAsync(\n  projectNativeRoot: string\n): Promise<ExtraDependencies | null> {\n  const extraMavenReposString = await resolveGradlePropertyAsync(\n    projectNativeRoot,\n    ANDROID_EXTRA_BUILD_DEPS_KEY\n  );\n  if (extraMavenReposString) {\n    try {\n      return JSON.parse(extraMavenReposString);\n    } catch {}\n  }\n  return null;\n}\n\nexport async function resolveGradlePropertyAsync(\n  projectNativeRoot: string,\n  propertyKey: string\n): Promise<string | null> {\n  const propsFile = path.join(projectNativeRoot, ANDROID_PROPERTIES_FILE);\n  try {\n    const contents = await fs.promises.readFile(propsFile, 'utf8');\n    const propertyValue = searchGradlePropertyFirst(contents, propertyKey);\n    if (propertyValue) {\n      return propertyValue;\n    }\n  } catch {}\n  return null;\n}\n\n/**\n * Generates the string to put into the generated package list.\n */\nasync function generatePackageListFileContentAsync(\n  modules: ModuleDescriptorAndroid[],\n  namespace: string\n): Promise<string> {\n  // TODO: Instead of ignoring `expo` here, make the package class paths configurable from `expo-module.config.json`.\n  const packagesClasses = await findAndroidPackagesAsync(\n    modules.filter((module) => module.packageName !== 'expo')\n  );\n\n  const modulesClasses = await findAndroidModules(modules);\n\n  return `package ${namespace};\n\nimport java.util.Arrays;\nimport java.util.List;\nimport expo.modules.core.interfaces.Package;\nimport expo.modules.kotlin.modules.Module;\nimport expo.modules.kotlin.ModulesProvider;\n\npublic class ExpoModulesPackageList implements ModulesProvider {\n  private static class LazyHolder {\n    static final List<Package> packagesList = Arrays.<Package>asList(\n${packagesClasses.map((packageClass) => `      new ${packageClass}()`).join(',\\n')}\n    );\n\n    static final List<Class<? extends Module>> modulesList = Arrays.<Class<? extends Module>>asList(\n      ${modulesClasses.map((moduleClass) => `      ${moduleClass}.class`).join(',\\n')}\n    );\n  }\n\n  public static List<Package> getPackageList() {\n    return LazyHolder.packagesList;\n  }\n\n  @Override\n  public List<Class<? extends Module>> getModulesList() {\n    return LazyHolder.modulesList;\n  }\n}\n`;\n}\n\nfunction findAndroidModules(modules: ModuleDescriptorAndroid[]): string[] {\n  const projects = modules.flatMap((module) => module.projects ?? []);\n\n  const modulesToProvide = projects.filter((project) => project.modules?.length > 0);\n  const classNames = ([] as string[]).concat(...modulesToProvide.map((module) => module.modules));\n  return classNames;\n}\n\nasync function findAndroidPackagesAsync(modules: ModuleDescriptorAndroid[]): Promise<string[]> {\n  const classes: string[] = [];\n\n  const flattenedSourceDirList: string[] = [];\n  for (const module of modules) {\n    for (const project of module.projects ?? []) {\n      flattenedSourceDirList.push(project.sourceDir);\n    }\n  }\n\n  await Promise.all(\n    flattenedSourceDirList.map(async (sourceDir) => {\n      const files = await glob('**/*Package.{java,kt}', {\n        cwd: sourceDir,\n      });\n\n      for (const file of files) {\n        const fileContent = await fs.promises.readFile(path.join(sourceDir, file), 'utf8');\n\n        const packageRegex = (() => {\n          if (process.env.EXPO_SHOULD_USE_LEGACY_PACKAGE_INTERFACE) {\n            return /\\bimport\\s+org\\.unimodules\\.core\\.(interfaces\\.Package|BasePackage)\\b/;\n          } else {\n            return /\\bimport\\s+expo\\.modules\\.core\\.(interfaces\\.Package|BasePackage)\\b/;\n          }\n        })();\n\n        // Very naive check to skip non-expo packages\n        if (!packageRegex.test(fileContent)) {\n          continue;\n        }\n\n        const classPathMatches = fileContent.match(/^package ([\\w.]+)\\b/m);\n\n        if (classPathMatches) {\n          const basename = path.basename(file, path.extname(file));\n          classes.push(`${classPathMatches[1]}.${basename}`);\n        }\n      }\n    })\n  );\n  return classes.sort();\n}\n\n/**\n * Converts the package name to Android's project name.\n *   `/` path will transform as `-`\n *\n * Example: `@expo/example` + `android/build.gradle` → `expo-example`\n */\nexport function convertPackageToProjectName(packageName: string): string {\n  return packageName.replace(/^@/g, '').replace(/\\W+/g, '-');\n}\n\n/**\n * Converts the package name and gradle file path to Android's project name.\n *   `$` to indicate subprojects\n *   `/` path will transform as `-`\n *\n * Example: `@expo/example` + `android/build.gradle` → `expo-example`\n *\n * Example: multiple projects\n *   - `expo-test` + `android/build.gradle` → `react-native-third-party`\n *   - `expo-test` + `subproject/build.gradle` → `react-native-third-party$subproject`\n */\nexport function convertPackageWithGradleToProjectName(\n  packageName: string,\n  buildGradleFile: string\n): string {\n  const name = convertPackageToProjectName(packageName);\n  const baseDir = path.dirname(buildGradleFile).replace(/\\//g, '-');\n  return baseDir === 'android' ? name : `${name}$${baseDir}`;\n}\n\n/**\n * Given the contents of a `gradle.properties` file,\n * searches for a property with the given name.\n *\n * This function will return the first property found with the given name.\n * The implementation follows config-plugins and\n * tries to align the behavior with the `withGradleProperties` plugin.\n */\nexport function searchGradlePropertyFirst(contents: string, propertyName: string): string | null {\n  const lines = contents.split('\\n');\n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i].trim();\n    if (line && !line.startsWith('#')) {\n      const eok = line.indexOf('=');\n      const key = line.slice(0, eok);\n      if (key === propertyName) {\n        const value = line.slice(eok + 1, line.length);\n        return value;\n      }\n    }\n  }\n  return null;\n}\n"]}