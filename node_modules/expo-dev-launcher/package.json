{"name": "expo-dev-launcher", "title": "Expo Development Launcher", "version": "5.1.16", "description": "Pre-release version of the Expo development launcher package for testing.", "main": "build/DevLauncher.js", "types": "build/DevLauncher.d.ts", "scripts": {"build": "expo-module build", "bundle": "./write_embedded_bundle.sh", "clean": "expo-module clean", "lint": "expo-module lint bundle && expo-module lint", "test": "expo-module test bundle && expo-module test", "prepare": "yarn tsc:bundle && expo-module prepare", "prepublishOnly": "yarn tsc:bundle && expo-module prepublishOnly", "expo-module": "expo-module", "start": "expo start --port 8090", "link:ios": "xcrun simctl openurl booted", "tsc:bundle": "tsc -p bundle/tsconfig.json"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/expo-dev-launcher"}, "keywords": ["react-native"], "author": "650 Industries, Inc.", "license": "MIT", "homepage": "https://docs.expo.dev", "dependencies": {"ajv": "8.11.0", "expo-dev-menu": "6.1.14", "expo-manifests": "~0.16.6", "resolve-from": "^5.0.0"}, "devDependencies": {"@babel/plugin-transform-export-namespace-from": "^7.23.4", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@react-navigation/stack": "^7.2.10", "@testing-library/jest-native": "^4.0.4", "@testing-library/react-native": "^13.1.0", "babel-plugin-module-resolver": "^5.0.0", "babel-preset-expo": "~13.2.3", "date-fns": "^2.28.0", "expo-dev-client-components": "2.1.5", "expo-module-scripts": "^4.1.9", "graphql": "^16.0.1", "graphql-request": "^3.6.1", "react": "19.0.0", "react-native": "0.79.5", "react-query": "^3.34.16", "url": "^0.11.0"}, "peerDependencies": {"expo": "*"}, "jest": {"preset": "react-native", "setupFilesAfterEnv": ["./setupTests.ts"], "testTimeout": 15000, "transformIgnorePatterns": ["/node_modules/(?!((jest-)?react-native|@react-native(-community)?)/|@react-navigation/)"]}, "gitHead": "1c4a89b0c0adebb53ef84b4a6ac25864e4652917"}