{"platforms": ["apple", "android"], "apple": {"podspecPath": "expo-dev-launcher.podspec", "swiftModuleName": "EXDevLauncher", "modules": ["DevLauncherInternal", "DevLauncherAuth", "RNCSafeAreaProviderManager"], "appDelegateSubscribers": ["ExpoDevLauncherAppDelegateSubscriber"], "reactDelegateHandlers": ["ExpoDevLauncherReactDelegateHandler"], "debugOnly": true}, "android": {"modules": ["expo.modules.devlauncher.modules.DevLauncherInternalModule"], "gradlePlugins": [{"id": "expo-dev-launcher-gradle-plugin", "group": "expo.modules", "sourceDir": "expo-dev-launcher-gradle-plugin"}]}}