plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

expoModule {
  canBePublished false
}

group = "host.exp.exponent"
version = "5.1.16"

android {
  namespace "expo.modules.devlauncher"
  defaultConfig {
    versionCode 9
    versionName "5.1.16"
  }

  buildTypes {
    buildTypes.each {
      it.buildConfigField 'String', 'VERSION', "\"${defaultConfig.versionName}\""
    }
  }

  buildFeatures {
    buildConfig true
    viewBinding true
  }
}

repositories {
  // ref: https://www.baeldung.com/maven-local-repository
  mavenLocal()
  maven {
    // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
    url "$rootDir/../node_modules/react-native/android"
  }
  maven {
    // Android JSC is installed from npm
    url "$rootDir/../node_modules/jsc-android/dist"
  }
  google()
}

dependencies {
  implementation project(":expo-dev-menu-interface")
  implementation project(":expo-manifests")
  implementation project(":expo-updates-interface")
  implementation project(":expo-dev-menu")

  implementation 'com.facebook.react:react-android'
  implementation 'com.facebook.soloader:soloader:0.11.0'

  implementation 'commons-io:commons-io:2.6'

  implementation 'com.squareup.okhttp3:okhttp:3.14.9'
  implementation 'com.google.code.gson:gson:2.8.6'

  // Fixes
  // Cannot access 'androidx....' which is a supertype of 'expo.modules.devmenu.DevMenuActivity'.
  // Check your module classpath for missing or conflicting dependencies
  api "androidx.appcompat:appcompat:1.1.0"
  api "androidx.lifecycle:lifecycle-extensions:2.2.0"

  implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.3")
  implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1")
  implementation "org.jetbrains.kotlin:kotlin-reflect:${kotlinVersion}"

  api project.dependencies.platform("io.insert-koin:koin-bom:3.5.6")
  api "io.insert-koin:koin-core"

  testImplementation 'androidx.test:core:1.4.0'
  testImplementation 'androidx.test:core-ktx:1.4.0'
  testImplementation "com.google.truth:truth:1.1.2"
  testImplementation 'com.squareup.okhttp3:mockwebserver:4.3.1'
  testImplementation "io.insert-koin:koin-test"
  testImplementation "io.insert-koin:koin-test-junit4"
  testImplementation 'io.mockk:mockk:1.12.3'
  testImplementation "org.robolectric:robolectric:4.10"
}
