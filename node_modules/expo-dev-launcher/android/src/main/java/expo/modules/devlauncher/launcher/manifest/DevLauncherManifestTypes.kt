package expo.modules.devlauncher.launcher.manifest

object DevLauncherOrientation {
  const val DEFAULT = "default"
  const val PORTRAIT = "portrait"
  const val LANDSCAPE = "landscape"
}

object DevLauncherUserInterface {
  const val AUTOMATIC = "automatic"
  const val DARK = "dark"
  const val LIGHT = "light"
}

object DevLauncherStatusBarStyle {
  const val DARK = "dark-content"
  const val LIGHT = "light-content"
}

object DevLauncherNavigationBarStyle {
  const val DARK = "dark-content"
  const val LIGHT = "light-content"
}

object DevLauncherNavigationBarVisibility {
  const val LEANBACK = "leanback"
  const val IMMERSIVE = "immersive"
  const val STICKY_IMMERSIVE = "sticky-immersive"
}
