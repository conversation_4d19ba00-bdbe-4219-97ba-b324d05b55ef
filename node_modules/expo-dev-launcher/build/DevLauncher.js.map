{"version": 3, "file": "DevLauncher.js", "sourceRoot": "", "sources": ["../src/DevLauncher.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAC7C,OAAO,wBAAwB,CAAC;AAOhC,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AAEjE;;GAEG;AACH,MAAM,UAAU,qBAAqB;IACnC,OAAO,CAAC,IAAI,CACV,qJAAqJ,CACtJ,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,kBAAkB;IAChC,OAAO,CAAC,CAAC,aAAa,CAAC,aAAa,CAAC;AACvC,CAAC", "sourcesContent": ["import { ExpoUpdatesManifest } from 'expo-manifests';\nimport { NativeModules } from 'react-native';\nimport './setUpErrorHandler.fx';\n\n/**\n * @hidden Dev launcher manifests are only ones served by servers (not embedded bare manifests)\n */\nexport type Manifest = ExpoUpdatesManifest;\n\nexport { disableErrorHandling } from './DevLauncherErrorManager';\n\n/**\n * @hidden\n */\nexport function registerErrorHandlers() {\n  console.warn(\n    'DevLauncher.registerErrorHandlers has been deprecated. To enable error handlers you need to import \"expo-dev-launcher\" at the top of your index.js.'\n  );\n}\n\n/**\n * A method that returns a boolean to indicate if the current application is a development build.\n */\nexport function isDevelopmentBuild(): boolean {\n  return !!NativeModules.EXDevLauncher;\n}\n\n/**\n * @hidden\n */\nexport type DevLauncherExtension = {\n  navigateToLauncherAsync: () => Promise<void>;\n};\n"]}