{"version": 3, "file": "WebUnsupportedError.js", "sourceRoot": "", "sources": ["../src/WebUnsupportedError.ts"], "names": [], "mappings": "AAAA,MAAM,CAAC,OAAO,OAAO,mBAAoB,SAAQ,KAAK;IACpD;QACE,KAAK,CAAC,kDAAkD,CAAC,CAAC;IAC5D,CAAC;CACF", "sourcesContent": ["export default class WebUnsupportedError extends Error {\n  constructor() {\n    super(\"`expo-dev-launcher` isn't supported on Expo Web.\");\n  }\n}\n"]}