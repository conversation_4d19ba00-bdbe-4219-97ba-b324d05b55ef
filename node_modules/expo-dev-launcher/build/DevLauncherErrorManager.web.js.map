{"version": 3, "file": "DevLauncherErrorManager.web.js", "sourceRoot": "", "sources": ["../src/DevLauncherErrorManager.web.ts"], "names": [], "mappings": "AAAA,OAAO,mBAAmB,MAAM,uBAAuB,CAAC;AAExD,MAAM,UAAU,oBAAoB;IAClC,MAAM,IAAI,mBAAmB,EAAE,CAAC;AAClC,CAAC", "sourcesContent": ["import WebUnsupportedError from './WebUnsupportedError';\n\nexport function disableErrorHandling() {\n  throw new WebUnsupportedError();\n}\n"]}