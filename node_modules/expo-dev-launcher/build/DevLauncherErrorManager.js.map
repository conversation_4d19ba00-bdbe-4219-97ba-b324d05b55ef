{"version": 3, "file": "DevLauncherErrorManager.js", "sourceRoot": "", "sources": ["../src/DevLauncherErrorManager.ts"], "names": [], "mappings": "AAKA,IAAI,sBAAsB,GAAG,IAAI,CAAC;AAClC,IAAI,MAAM,GAAG,KAAK,CAAC,CAAC,gDAAgD;AAEpE,MAAM,iCAAiC,GAAG;;;;mGAIyD,CAAC;AAEpG,MAAM,gCAAgC,GAAG;;;mGAG0D,CAAC;AAEpG,SAAS,2BAA2B,CAAC,KAAiB;IACpD,KAAK,CAAC,OAAO,IAAI,MAAM,GAAG,iCAAiC,CAAC;AAC9D,CAAC;AAED,SAAS,+BAA+B,CAAC,KAAY;IACnD,KAAK,CAAC,OAAO,GAAG;;EAEhB,gCAAgC,EAAE,CAAC;AACrC,CAAC;AAED,SAAS,cAAc,CAAC,KAAyB;IAC/C,IAAI,MAAM,IAAI,KAAK,EAAE,CAAC;QACpB,sCAAsC;QACtC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,iBAAiB,CAAC,CAAC,CAAC;gBACvB,2BAA2B,CAAC,KAAK,CAAC,CAAC;gBACnC,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;SAAM,IACL,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,8BAA8B,CAAC,IAAI,4BAA4B;QACtF,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,0DAA0D,CAAC,CAAC,mBAAmB;MACtG,CAAC;QACD,+BAA+B,CAAC,KAAK,CAAC,CAAC;IACzC,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CAAC,eAAqC,EAAE,KAAU,EAAE,OAAiB;IACxF,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;QAC3B,gGAAgG;QAChG,oGAAoG;QACpG,IAAI,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,6CAA6C,CAAC,EAAE,CAAC;YACrF,OAAO;QACT,CAAC;QACD,cAAc,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC;IAED,MAAM,GAAG,IAAI,CAAC;IACd,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AAClC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,eAAqC;IACtE,OAAO,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;QACxB,IAAI,sBAAsB,EAAE,CAAC;YAC3B,YAAY,CAAC,eAAe,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;YAC9C,OAAO;QACT,CAAC;QAED,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAClC,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB;IAClC,sBAAsB,GAAG,KAAK,CAAC;AACjC,CAAC", "sourcesContent": ["import type { ErrorHandlerCallback } from 'react-native';\n\n// Similar interface to the one used in expo modules.\ntype CodedError = Error & { code?: string };\n\nlet isErrorHandlingEnabled = true;\nlet wasHit = false; // whether the original error handler was called\n\nconst unavailableErrorPossibleSolutions = `Some possible solutions:\n- Make sure that the method is available on the current platform.\n- Make sure you're using the newest available version of this development client.\n- Make sure you're running a compatible version of your JavaScript code.\n- If you've installed a new library recently, you may need to make a new development client build.`;\n\nconst moduleIsMissingPossibleSolutions = `Some possible solutions:\n- Make sure you're using the newest available version of this development client.\n- Make sure you're running a compatible version of your JavaScript code.\n- If you've installed a new library recently, you may need to make a new development client build.`;\n\nfunction customizeUnavailableMessage(error: CodedError) {\n  error.message += '\\n\\n' + unavailableErrorPossibleSolutions;\n}\n\nfunction customizeModuleIsMissingMessage(error: Error) {\n  error.message = `Your JavaScript code tried to access a native module that doesn't exist in this development client. \n\n${moduleIsMissingPossibleSolutions}`;\n}\n\nfunction customizeError(error: Error | CodedError) {\n  if ('code' in error) {\n    // It's a CodedError from expo modules\n    switch (error.code) {\n      case 'ERR_UNAVAILABLE': {\n        customizeUnavailableMessage(error);\n        break;\n      }\n    }\n  } else if (\n    error.message.includes('Native module cannot be null') || // RN 0.64 and below message\n    error.message.includes('`new NativeEventEmitter()` requires a non-null argument.') // RN 0.65+ message\n  ) {\n    customizeModuleIsMissingMessage(error);\n  }\n}\n\nfunction errorHandler(originalHandler: ErrorHandlerCallback, error: any, isFatal?: boolean) {\n  if (error instanceof Error) {\n    // Suppresses `\"main\" has not been registered` error only if it was caused by a different error.\n    // Otherwise, we want to show it, cause the user may forget to call `AppRegistry.registerComponent`.\n    if (wasHit && error.message?.includes('has not been registered. This can happen if')) {\n      return;\n    }\n    customizeError(error);\n  }\n\n  wasHit = true;\n  originalHandler(error, isFatal);\n}\n\n/**\n * @hidden\n */\nexport function createErrorHandler(originalHandler: ErrorHandlerCallback): ErrorHandlerCallback {\n  return (error, isFatal) => {\n    if (isErrorHandlingEnabled) {\n      errorHandler(originalHandler, error, isFatal);\n      return;\n    }\n\n    originalHandler(error, isFatal);\n  };\n}\n\n/**\n * @hidden\n */\nexport function disableErrorHandling() {\n  isErrorHandlingEnabled = false;\n}\n"]}