{"version": 3, "file": "setUpErrorHandler.fx.js", "sourceRoot": "", "sources": ["../src/setUpErrorHandler.fx.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,2BAA2B,CAAC;AAE/D,MAAM,aAAa,GAAG,UAAU,CAAC,gBAAgB,EAAE,CAAC;AACpD,UAAU,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC,CAAC", "sourcesContent": ["import { createErrorHandler } from './DevLauncherErrorManager';\n\nconst globalHandler = ErrorUtils.getGlobalHandler();\nErrorUtils.setGlobalHandler(createErrorHandler(globalHandler));\n"]}