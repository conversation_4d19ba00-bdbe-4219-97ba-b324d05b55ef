{"formatVersion": "1.1", "component": {"group": "BareExpo", "module": "expo.modules.image", "version": "2.3.2", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.13"}}, "variants": [{"name": "releaseVariantReleaseApiPublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.github.bumptech.glide", "module": "glide", "version": {"requires": "4.16.0"}}, {"group": "com.caverock", "module": "androidsvg-aar", "version": {"requires": "1.4"}}, {"group": "com.github.bumptech.glide", "module": "okhttp3-integration", "version": {"requires": "4.11.0"}}, {"group": "com.squareup.okhttp3", "module": "okhttp", "version": {"requires": "4.9.2"}}], "files": [{"name": "expo.modules.image-2.3.2.aar", "url": "expo.modules.image-2.3.2.aar", "size": 279544, "sha512": "47c107c9a1134bb50953e5cf037cdc5aef8088d6c1bce906d268f2cf2c6b1a2692dbbd4cf686922d9eded7a78e275fea8038b91267fffd31957fa68b297bdae8", "sha256": "5d682f47a7031c3d5403faf294bb898f52299dcc73903682475cc310c39e70ac", "sha1": "af130ea05cd1ce374351bdb409b187b699baeef7", "md5": "5c50faf7e63489ecc2e5d3ab07a2f707"}]}, {"name": "releaseVariantReleaseRuntimePublication", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "aar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.jetbrains.kotlin", "module": "kotlin-stdlib-jdk7", "version": {"requires": "2.0.21"}}, {"group": "com.facebook.react", "module": "react-android"}, {"group": "com.github.penfeizhou.android.animation", "module": "glide-plugin", "version": {"requires": "3.0.5"}}, {"group": "com.github.bumptech.glide", "module": "avif-integration", "version": {"requires": "4.16.0"}}, {"group": "org.jetbrains.kotlinx", "module": "kotlinx-coroutines-core", "version": {"requires": "1.5.1"}}, {"group": "jp.was<PERSON><PERSON>", "module": "glide-transformations", "version": {"requires": "4.3.0"}}, {"group": "com.github.bumptech.glide", "module": "glide", "version": {"requires": "4.16.0"}}, {"group": "com.caverock", "module": "androidsvg-aar", "version": {"requires": "1.4"}}, {"group": "com.github.bumptech.glide", "module": "okhttp3-integration", "version": {"requires": "4.11.0"}}, {"group": "com.squareup.okhttp3", "module": "okhttp", "version": {"requires": "4.9.2"}}], "files": [{"name": "expo.modules.image-2.3.2.aar", "url": "expo.modules.image-2.3.2.aar", "size": 279544, "sha512": "47c107c9a1134bb50953e5cf037cdc5aef8088d6c1bce906d268f2cf2c6b1a2692dbbd4cf686922d9eded7a78e275fea8038b91267fffd31957fa68b297bdae8", "sha256": "5d682f47a7031c3d5403faf294bb898f52299dcc73903682475cc310c39e70ac", "sha1": "af130ea05cd1ce374351bdb409b187b699baeef7", "md5": "5c50faf7e63489ecc2e5d3ab07a2f707"}]}, {"name": "releaseVariantReleaseSourcePublication", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "expo.modules.image-2.3.2-sources.jar", "url": "expo.modules.image-2.3.2-sources.jar", "size": 64200, "sha512": "4fd35480bc1edafe3727afbfb6b1d9cf69643f30523402e7512a8b2944d17915150373225d5aed1c344b80ad9c9b3fe1ff1050b2c83a13c8faa34956ddef588e", "sha256": "d91f60ce8ec04a24413f687b5596f0165504d5b55d05c380daea5b155e537100", "sha1": "9d61b3b9a11effe5d06c77755eaa06d86da5d422", "md5": "754e5c8b8c3d801d36965a6cf0ec673a"}]}]}