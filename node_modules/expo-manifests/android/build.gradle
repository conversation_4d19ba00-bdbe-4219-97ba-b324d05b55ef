plugins {
  id 'com.android.library'
  id 'expo-module-gradle-plugin'
}

group = 'host.exp.exponent'
version = '0.16.6'

expoModule {
  canBePublished false
}

android {
  namespace "expo.modules.manifests"
  defaultConfig {
    versionCode 31
    versionName '0.16.6'
    testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
  }
  testOptions {
    unitTests.includeAndroidResources = true
  }
  sourceSets {
    androidTest.assets.srcDirs += files("$projectDir/src/androidTest/schemas".toString())
  }
}

dependencies {
  implementation project(':expo-json-utils')

  testImplementation 'junit:junit:4.13.2'
  testImplementation 'androidx.test:core:1.5.0'
  testImplementation 'org.mockito:mockito-core:4.0.0'
  testImplementation 'io.mockk:mockk:1.13.5'
  testImplementation 'org.json:json:20230227'
  testImplementation "com.google.truth:truth:1.1.2"
}
