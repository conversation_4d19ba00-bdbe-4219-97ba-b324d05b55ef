{"version": 3, "file": "Manifests.js", "sourceRoot": "", "sources": ["../src/Manifests.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { ExpoConfig } from 'expo/config';\n\n// @docsMissing\nexport type ManifestAsset = {\n  url: string;\n};\n\nexport type ExpoClientConfig = ExpoConfig & {\n  /**\n   * Only present during development using `@expo/cli`.\n   */\n  hostUri?: string;\n};\n\n// @docsMissing\nexport type ManifestExtra = ClientScopingConfig & {\n  expoClient?: ExpoClientConfig;\n  expoGo?: ExpoGoConfig;\n  eas?: EASConfig;\n};\n\n// @needsAudit\nexport type EASConfig = {\n  /**\n   * The ID for this project if it's using EAS. UUID. This value will not change when a project is\n   * transferred between accounts or renamed.\n   */\n  projectId?: string;\n};\n\n// @needsAudit\nexport type ClientScopingConfig = {\n  /**\n   * An opaque unique string for scoping client-side data to this project. This value\n   * will not change when a project is transferred between accounts or renamed.\n   */\n  scopeKey?: string;\n};\n\n// @docsMissing\nexport type ExpoGoConfig = {\n  mainModuleName?: string;\n  debuggerHost?: string;\n  developer?: Record<string, any> & {\n    tool?: string;\n  };\n  packagerOpts?: ExpoGoPackagerOpts;\n};\n\n// @docsMissing\nexport type ExpoGoPackagerOpts = Record<string, any> & {\n  hostType?: string;\n  dev?: boolean;\n  strict?: boolean;\n  minify?: boolean;\n  urlType?: string;\n  urlRandomness?: string;\n  lanType?: string;\n};\n\n/**\n * A `expo-updates` manifest.\n */\nexport type ExpoUpdatesManifest = {\n  id: string;\n  createdAt: string;\n  runtimeVersion: string;\n  launchAsset: ManifestAsset;\n  assets: ManifestAsset[];\n  metadata: object;\n  extra?: ManifestExtra;\n};\n\n/**\n * @deprecated renamed to `ExpoUpdatesManifest`, will be removed in a few versions.\n */\nexport type NewManifest = ExpoUpdatesManifest;\n\n/**\n * An embedded manifest.\n *\n * Generated during build in **createManifest.js** build step script.\n */\nexport type EmbeddedManifest = {\n  id: string;\n  commitTime: number;\n  assets: any[]; // intentionally underspecified for now since there are no uses in JS\n};\n\n/**\n * @deprecated Renamed to `EmbeddedManifest`, will be removed in a few versions.\n */\nexport type BareManifest = EmbeddedManifest;\n"]}