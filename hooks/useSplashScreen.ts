import { useState, useEffect } from 'react';
import * as SplashScreen from 'expo-splash-screen';

export function useSplashScreen() {
  const [isReady, setIsReady] = useState(false);
  const [showCustomSplash, setShowCustomSplash] = useState(true);

  useEffect(() => {
    async function prepare() {
      try {
        // Keep the native splash screen visible while we prepare the app
        await SplashScreen.preventAutoHideAsync();
        
        // Simulate app loading (you can replace this with actual loading logic)
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setIsReady(true);
      } catch (e) {
        console.warn(e);
      }
    }

    prepare();
  }, []);

  const onCustomSplashFinish = async () => {
    setShowCustomSplash(false);
    // Hide the native splash screen
    await SplashScreen.hideAsync();
  };

  return {
    isReady,
    showCustomSplash,
    onCustomSplashFinish,
  };
}
