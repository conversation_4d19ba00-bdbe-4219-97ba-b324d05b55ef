{"expo": {"name": "NurServ Nurse App", "slug": "nurserv-nurse-app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/favicon.svg", "scheme": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "userInterfaceStyle": "automatic", "newArchEnabled": true, "developmentClient": {"silentLaunch": true}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.nurserv.nurseapp"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/favicon.svg", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.nurserv.nurseapp"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.svg"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash.png", "resizeMode": "cover", "backgroundColor": "#1BA3C7"}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "7980b8ca-826a-4c10-b8ca-c414bfb82ae1"}}}}