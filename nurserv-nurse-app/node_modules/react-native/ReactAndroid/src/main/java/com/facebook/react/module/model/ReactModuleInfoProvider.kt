/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.facebook.react.module.model

/** Interface for auto-generated class by ReactModuleSpecProcessor. */
public fun interface ReactModuleInfoProvider {
  public fun getReactModuleInfos(): Map<String, ReactModuleInfo>
}
